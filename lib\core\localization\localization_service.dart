import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../app/data/models/entities/sittings_model.dart';
import '../../app/services/services_impl/appservices.dart';

/// Comprehensive localization service that manages language switching,
/// RTL support, and integrates with ObjectBox storage
class LocalizationService extends GetxService {
  static LocalizationService get to => Get.find();

  // Supported locales
  static const List<Locale> supportedLocales = [
    Locale('en', 'US'), // English (United States)
    Locale('ar', 'SA'), // Arabic (Saudi Arabia)
  ];

  // Default locale
  static const Locale defaultLocale = Locale('en', 'US');
  static const Locale fallbackLocale = Locale('ar', 'SA');

  // Current locale reactive variable
  final Rx<Locale> _currentLocale = defaultLocale.obs;
  Locale get currentLocale => _currentLocale.value;

  // Text direction reactive variable
  final Rx<TextDirection> _textDirection = TextDirection.ltr.obs;
  TextDirection get textDirection => _textDirection.value;

  // Language names for UI display
  static const Map<String, String> languageNames = {
    'en_US': 'English',
    'ar_SA': 'العربية',
  };

  // ObjectBox service for persistence
  late ObjectboxService _objectboxService;
  late Sittings _settings;

  @override
  Future<void> onInit() async {
    super.onInit();
    _objectboxService = Get.find<ObjectboxService>();
    await _initializeLocale();
  }

  /// Initialize locale from stored settings or device locale
  Future<void> _initializeLocale() async {
    try {
      // Try to get stored settings
      _settings = _objectboxService.objectbox.appBox.get(1) ?? Sittings(id: 1);
      
      Locale? storedLocale;
      if (_settings.language != null) {
        storedLocale = _parseLocaleFromString(_settings.language!);
      }

      // Use stored locale, device locale, or default locale
      Locale initialLocale = storedLocale ?? 
                            _getDeviceLocale() ?? 
                            defaultLocale;

      await changeLocale(initialLocale, saveToStorage: false);
    } catch (e) {
      print('Error initializing locale: $e');
      await changeLocale(defaultLocale, saveToStorage: false);
    }
  }

  /// Get device locale if supported, otherwise return null
  Locale? _getDeviceLocale() {
    final deviceLocale = Get.deviceLocale;
    if (deviceLocale != null) {
      // Check if device locale is supported
      for (final supportedLocale in supportedLocales) {
        if (supportedLocale.languageCode == deviceLocale.languageCode) {
          return supportedLocale;
        }
      }
    }
    return null;
  }

  /// Parse locale from string format (e.g., "en_US" or "ar_SA")
  Locale _parseLocaleFromString(String localeString) {
    final parts = localeString.split('_');
    if (parts.length == 2) {
      return Locale(parts[0], parts[1]);
    } else {
      // Handle legacy format (e.g., "en" or "ar")
      switch (parts[0]) {
        case 'en':
          return const Locale('en', 'US');
        case 'ar':
          return const Locale('ar', 'SA');
        default:
          return defaultLocale;
      }
    }
  }

  /// Convert locale to string format for storage
  String _localeToString(Locale locale) {
    return '${locale.languageCode}_${locale.countryCode}';
  }

  /// Change the current locale
  Future<void> changeLocale(Locale newLocale, {bool saveToStorage = true}) async {
    try {
      // Validate locale is supported
      if (!supportedLocales.contains(newLocale)) {
        print('Unsupported locale: $newLocale');
        return;
      }

      // Update current locale
      _currentLocale.value = newLocale;

      // Update text direction based on language
      _textDirection.value = _isRTL(newLocale) ? TextDirection.rtl : TextDirection.ltr;

      // Update GetX locale
      Get.updateLocale(newLocale);

      // Save to storage if requested
      if (saveToStorage) {
        await _saveLocaleToStorage(newLocale);
      }

      print('Locale changed to: ${_localeToString(newLocale)}');
    } catch (e) {
      print('Error changing locale: $e');
    }
  }

  /// Save locale to ObjectBox storage
  Future<void> _saveLocaleToStorage(Locale locale) async {
    try {
      _settings.language = _localeToString(locale);
      _objectboxService.objectbox.appBox.put(_settings);
      print('Locale saved to storage: ${_settings.language}');
    } catch (e) {
      print('Error saving locale to storage: $e');
    }
  }

  /// Check if locale uses RTL text direction
  bool _isRTL(Locale locale) {
    return locale.languageCode == 'ar' || 
           locale.languageCode == 'he' || 
           locale.languageCode == 'fa' || 
           locale.languageCode == 'ur';
  }

  /// Get current language code
  String get currentLanguageCode => _currentLocale.value.languageCode;

  /// Get current country code
  String? get currentCountryCode => _currentLocale.value.countryCode;

  /// Get current locale string
  String get currentLocaleString => _localeToString(_currentLocale.value);

  /// Check if current locale is RTL
  bool get isRTL => _isRTL(_currentLocale.value);

  /// Check if current locale is Arabic
  bool get isArabic => _currentLocale.value.languageCode == 'ar';

  /// Check if current locale is English
  bool get isEnglish => _currentLocale.value.languageCode == 'en';

  /// Get display name for current language
  String get currentLanguageName => languageNames[currentLocaleString] ?? currentLanguageCode;

  /// Get display name for a specific locale
  String getLanguageName(Locale locale) {
    return languageNames[_localeToString(locale)] ?? locale.languageCode;
  }

  /// Toggle between supported languages
  Future<void> toggleLanguage() async {
    if (isEnglish) {
      await changeLocale(const Locale('ar', 'SA'));
    } else {
      await changeLocale(const Locale('en', 'US'));
    }
  }

  /// Get list of supported locales for UI
  List<Locale> get availableLocales => List.unmodifiable(supportedLocales);

  /// Get locale-specific text alignment
  TextAlign get textAlign => isRTL ? TextAlign.right : TextAlign.left;

  /// Get locale-specific text alignment for start
  TextAlign get textAlignStart => isRTL ? TextAlign.right : TextAlign.left;

  /// Get locale-specific text alignment for end
  TextAlign get textAlignEnd => isRTL ? TextAlign.left : TextAlign.right;

  /// Get locale-specific edge insets (for RTL support)
  EdgeInsets getDirectionalPadding({
    double start = 0.0,
    double top = 0.0,
    double end = 0.0,
    double bottom = 0.0,
  }) {
    return isRTL
        ? EdgeInsets.only(left: end, top: top, right: start, bottom: bottom)
        : EdgeInsets.only(left: start, top: top, right: end, bottom: bottom);
  }

  /// Get locale-specific margin
  EdgeInsets getDirectionalMargin({
    double start = 0.0,
    double top = 0.0,
    double end = 0.0,
    double bottom = 0.0,
  }) {
    return getDirectionalPadding(start: start, top: top, end: end, bottom: bottom);
  }

  /// Format numbers according to locale
  String formatNumber(num number) {
    // For Arabic, you might want to use Arabic-Indic digits
    if (isArabic) {
      // Convert to Arabic-Indic numerals if needed
      return number.toString();
    }
    return number.toString();
  }

  /// Get appropriate font family for current locale
  String? get fontFamily {
    if (isArabic) {
      // Return Arabic font family if you have one
      return null; // Use system default for now
    }
    return null; // Use system default
  }

  /// Dispose resources
  @override
  void onClose() {
    super.onClose();
  }
}
