abstract class CacheRepository {
  Future<void> put<T>(String key, T value, {Duration? expiry});
  Future<T?> get<T>(String key);
  Future<bool> contains(String key);
  Future<void> remove(String key);
  Future<void> clear();
  Future<void> clearExpired();
  Future<int> size();
  Future<List<String>> keys();
  Stream<CacheEvent> get cacheEventStream;
  Future<void> setMaxSize(int maxSize);
  Future<void> evictLeastRecentlyUsed();
}