// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:objectbox/objectbox.dart';

//flutter pub run build_runner build

@Entity()
class Sittings {
  int id; // device id
  String? passCodeLock;
  bool? isLoggedIn;
  bool? isDarkMode;
  String? localizations;
  String? language;
  String? deviceName;
  //bool? downloadThruMobileData;// Sync settings (e.g., Wi-Fi only, always)
  bool? notificationEnabled;
  bool? backgroundWorkEnabled;

  Sittings({
    this.id = 0,
    this.isLoggedIn,
    this.isDarkMode,
    this.localizations,
    this.language,
    this.deviceName,
    this.notificationEnabled,
  });

  // Map settings;
}
