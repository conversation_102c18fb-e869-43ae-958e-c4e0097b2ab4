import 'app_services_interface.dart';

/// Interface for ObjectBox database operations
abstract class IObjectboxService extends IInitializableService {
  /// Get the ObjectBox instance
  dynamic get objectbox;
  
  /// Get the ObjectBox instance (alias for objectbox)
  dynamic get instance;
  
  /// Initialize ObjectBox database
  Future<void> initializeDatabase();
  
  /// Close the database connection
  Future<void> closeDatabase();
  
  /// Check if database is open and ready
  bool get isDatabaseReady;
  
  /// Get app box for general app data
  dynamic get appBox;
  
  /// Get settings box for user settings
  dynamic get settingsBox;
  
  /// Backup database
  Future<bool> backupDatabase(String path);
  
  /// Restore database from backup
  Future<bool> restoreDatabase(String path);
  
  /// Get database statistics
  Map<String, dynamic> getDatabaseStats();
}
