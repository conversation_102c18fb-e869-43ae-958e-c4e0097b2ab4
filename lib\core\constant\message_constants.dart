/// Message constants for user-facing text and error messages
class MessageConstants {
  MessageConstants._();

  // Success Messages
  static const String loginSuccess = 'Login successful';
  static const String registrationSuccess = 'Registration successful';
  static const String logoutSuccess = 'Logout successful';
  static const String profileUpdated = 'Profile updated successfully';
  static const String passwordChanged = 'Password changed successfully';
  static const String emailVerified = 'Email verified successfully';
  static const String dataLoaded = 'Data loaded successfully';
  static const String dataSaved = 'Data saved successfully';
  static const String dataDeleted = 'Data deleted successfully';
  static const String fileUploaded = 'File uploaded successfully';
  static const String settingsUpdated = 'Settings updated successfully';

  // Error Messages
  static const String genericError = 'Something went wrong. Please try again.';
  static const String networkError =
      'Network error. Please check your connection.';
  static const String serverError = 'Server error. Please try again later.';
  static const String timeoutError = 'Request timeout. Please try again.';
  static const String unauthorizedError =
      'Unauthorized access. Please login again.';
  static const String forbiddenError =
      'Access forbidden. You don\'t have permission.';
  static const String notFoundError = 'Requested resource not found.';
  static const String validationError =
      'Please check your input and try again.';
  static const String cacheError = 'Cache error. Please clear app data.';
  static const String formatError = 'Invalid data format received.';

  // Authentication Messages
  static const String invalidCredentials = 'Invalid email or password';
  static const String accountNotFound = 'Account not found';
  static const String accountDisabled = 'Account has been disabled';
  static const String emailNotVerified = 'Please verify your email address';
  static const String passwordResetSent =
      'Password reset link sent to your email';
  static const String invalidToken = 'Invalid or expired token';
  static const String sessionExpired = 'Session expired. Please login again';
  static const String accountLocked =
      'Account locked due to multiple failed attempts';

  // Validation Messages
  static const String emailRequired = 'Email is required';
  static const String emailInvalid = 'Please enter a valid email address';
  static const String passwordRequired = 'Password is required';
  static const String passwordTooShort =
      'Password must be at least 8 characters';
  static const String passwordTooWeak =
      'Password must contain uppercase, lowercase, number and special character';
  static const String passwordMismatch = 'Passwords do not match';
  static const String nameRequired = 'Name is required';
  static const String phoneRequired = 'Phone number is required';
  static const String phoneInvalid = 'Please enter a valid phone number';
  static const String fieldRequired = 'This field is required';
  static const String fieldTooLong = 'Input is too long';
  static const String fieldTooShort = 'Input is too short';

  // Loading Messages
  static const String loading = 'Loading...';
  static const String pleaseWait = 'Please wait...';
  static const String processing = 'Processing...';
  static const String uploading = 'Uploading...';
  static const String downloading = 'Downloading...';
  static const String saving = 'Saving...';
  static const String deleting = 'Deleting...';
  static const String refreshing = 'Refreshing...';

  // Empty State Messages
  static const String noData = 'No data available';
  static const String noResults = 'No results found';
  static const String noNotifications = 'No notifications';
  static const String noMessages = 'No messages';
  static const String noItems = 'No items found';
  static const String emptyList = 'List is empty';
  static const String noConnection = 'No internet connection';

  // Confirmation Messages
  static const String confirmDelete =
      'Are you sure you want to delete this item?';
  static const String confirmLogout = 'Are you sure you want to logout?';
  static const String confirmExit = 'Are you sure you want to exit?';
  static const String confirmCancel = 'Are you sure you want to cancel?';
  static const String confirmSave = 'Do you want to save changes?';
  static const String confirmDiscard = 'Discard changes?';

  // Action Messages
  static const String retry = 'Retry';
  static const String cancel = 'Cancel';
  static const String ok = 'OK';
  static const String yes = 'Yes';
  static const String no = 'No';
  static const String save = 'Save';
  static const String delete = 'Delete';
  static const String edit = 'Edit';
  static const String update = 'Update';
  static const String submit = 'Submit';
  static const String send = 'Send';
  static const String back = 'Back';
  static const String next = 'Next';
  static const String previous = 'Previous';
  static const String done = 'Done';
  static const String close = 'Close';
  static const String refresh = 'Refresh';

  // Permission Messages
  static const String cameraPermissionRequired =
      'Camera permission is required';
  static const String storagePermissionRequired =
      'Storage permission is required';
  static const String locationPermissionRequired =
      'Location permission is required';
  static const String microphonePermissionRequired =
      'Microphone permission is required';
  static const String permissionDenied = 'Permission denied';
  static const String permissionPermanentlyDenied =
      'Permission permanently denied. Please enable from settings.';

  // File Messages
  static const String fileNotFound = 'File not found';
  static const String fileTooLarge = 'File size is too large';
  static const String invalidFileType = 'Invalid file type';
  static const String uploadFailed = 'File upload failed';
  static const String downloadFailed = 'File download failed';

  // Connectivity Messages
  static const String connectionLost = 'Connection lost';
  static const String connectionRestored = 'Connection restored';
  static const String offline = 'You are offline';
  static const String online = 'You are online';

  // Update Messages
  static const String updateAvailable = 'Update available';
  static const String updateRequired = 'Update required to continue';
  static const String updateLater = 'Update later';
  static const String updateNow = 'Update now';
}
