import 'package:equatable/equatable.dart';

/// Base model class that all models should extend
abstract class BaseModel extends Equatable {
  const BaseModel();

  /// Convert model to JSON
  Map<String, dynamic> toJson();

  /// Create model from JSON
  /// This should be implemented by each concrete model
  // static T fromJson<T extends BaseModel>(Map<String, dynamic> json);

  @override
  List<Object?> get props => [];
}

/// Base response model for API responses
class BaseResponse<T> extends BaseModel {
  final bool success;
  final String? message;
  final T? data;
  final int? statusCode;
  final String? error;

  const BaseResponse({
    required this.success,
    this.message,
    this.data,
    this.statusCode,
    this.error,
  });

  factory BaseResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic)? fromJsonT,
  ) {
    return BaseResponse<T>(
      success: json['success'] ?? false,
      message: json['message'],
      data: json['data'] != null && fromJsonT != null 
          ? fromJsonT(json['data']) 
          : json['data'],
      statusCode: json['statusCode'],
      error: json['error'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data,
      'statusCode': statusCode,
      'error': error,
    };
  }

  @override
  List<Object?> get props => [success, message, data, statusCode, error];
}

/// Base list response model for paginated API responses
class BaseListResponse<T> extends BaseModel {
  final bool success;
  final String? message;
  final List<T> data;
  final PaginationMeta? pagination;
  final int? statusCode;
  final String? error;

  const BaseListResponse({
    required this.success,
    this.message,
    required this.data,
    this.pagination,
    this.statusCode,
    this.error,
  });

  factory BaseListResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    return BaseListResponse<T>(
      success: json['success'] ?? false,
      message: json['message'],
      data: json['data'] != null
          ? (json['data'] as List)
              .map((item) => fromJsonT(item as Map<String, dynamic>))
              .toList()
          : [],
      pagination: json['pagination'] != null
          ? PaginationMeta.fromJson(json['pagination'])
          : null,
      statusCode: json['statusCode'],
      error: json['error'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data.map((item) => (item as BaseModel).toJson()).toList(),
      'pagination': pagination?.toJson(),
      'statusCode': statusCode,
      'error': error,
    };
  }

  @override
  List<Object?> get props => [success, message, data, pagination, statusCode, error];
}

/// Pagination metadata model
class PaginationMeta extends BaseModel {
  final int currentPage;
  final int totalPages;
  final int totalItems;
  final int itemsPerPage;
  final bool hasNextPage;
  final bool hasPreviousPage;

  const PaginationMeta({
    required this.currentPage,
    required this.totalPages,
    required this.totalItems,
    required this.itemsPerPage,
    required this.hasNextPage,
    required this.hasPreviousPage,
  });

  factory PaginationMeta.fromJson(Map<String, dynamic> json) {
    return PaginationMeta(
      currentPage: json['currentPage'] ?? 1,
      totalPages: json['totalPages'] ?? 1,
      totalItems: json['totalItems'] ?? 0,
      itemsPerPage: json['itemsPerPage'] ?? 20,
      hasNextPage: json['hasNextPage'] ?? false,
      hasPreviousPage: json['hasPreviousPage'] ?? false,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'currentPage': currentPage,
      'totalPages': totalPages,
      'totalItems': totalItems,
      'itemsPerPage': itemsPerPage,
      'hasNextPage': hasNextPage,
      'hasPreviousPage': hasPreviousPage,
    };
  }

  @override
  List<Object?> get props => [
        currentPage,
        totalPages,
        totalItems,
        itemsPerPage,
        hasNextPage,
        hasPreviousPage,
      ];
}

/// Base entity model for database entities
abstract class BaseEntity extends BaseModel {
  final String id;
  final DateTime createdAt;
  final DateTime updatedAt;

  const BaseEntity({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [id, createdAt, updatedAt];
}

/// User model example
class User extends BaseEntity {
  final String email;
  final String username;
  final String? firstName;
  final String? lastName;
  final String? avatar;
  final bool isActive;

  const User({
    required super.id,
    required super.createdAt,
    required super.updatedAt,
    required this.email,
    required this.username,
    this.firstName,
    this.lastName,
    this.avatar,
    this.isActive = true,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      email: json['email'],
      username: json['username'],
      firstName: json['firstName'],
      lastName: json['lastName'],
      avatar: json['avatar'],
      isActive: json['isActive'] ?? true,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'email': email,
      'username': username,
      'firstName': firstName,
      'lastName': lastName,
      'avatar': avatar,
      'isActive': isActive,
    };
  }

  String get fullName {
    if (firstName != null && lastName != null) {
      return '$firstName $lastName';
    } else if (firstName != null) {
      return firstName!;
    } else if (lastName != null) {
      return lastName!;
    } else {
      return username;
    }
  }

  User copyWith({
    String? email,
    String? username,
    String? firstName,
    String? lastName,
    String? avatar,
    bool? isActive,
  }) {
    return User(
      id: id,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
      email: email ?? this.email,
      username: username ?? this.username,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      avatar: avatar ?? this.avatar,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  List<Object?> get props => [
        ...super.props,
        email,
        username,
        firstName,
        lastName,
        avatar,
        isActive,
      ];
}
