import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:dartz/dartz.dart';
import 'package:deewan/core/errors/failures.dart';
import 'package:deewan/core/enums/enums.dart';

/// Base controller class that all controllers should extend
abstract class BaseController extends GetxController {
  // Loading state
  final _isLoading = false.obs;
  bool get isLoading => _isLoading.value;
  set isLoading(bool value) => _isLoading.value = value;

  // Error state
  final _error = Rxn<String>();
  String? get error => _error.value;
  set error(String? value) => _error.value = value;

  // Status request state
  final _statusRequest = StatusRequest.success.obs;
  StatusRequest get statusRequest => _statusRequest.value;
  set statusRequest(StatusRequest value) => _statusRequest.value = value;

  /// Handle API response with loading states
  Future<T?> handleRequest<T>(
    Future<Either<Failure, T>> Function() request, {
    bool showLoading = true,
    bool showError = true,
    String? successMessage,
  }) async {
    try {
      if (showLoading) {
        isLoading = true;
        statusRequest = StatusRequest.loading;
      }
      
      error = null;

      final result = await request();

      return result.fold(
        (failure) {
          error = failure.message;
          statusRequest = _getStatusFromFailure(failure);
          
          if (showError) {
            showErrorSnackbar(failure.message);
          }
          
          return null;
        },
        (data) {
          statusRequest = StatusRequest.success;
          
          if (successMessage != null) {
            showSuccessSnackbar(successMessage);
          }
          
          return data;
        },
      );
    } catch (e) {
      error = 'An unexpected error occurred';
      statusRequest = StatusRequest.failure;
      
      if (showError) {
        showErrorSnackbar('An unexpected error occurred');
      }
      
      return null;
    } finally {
      if (showLoading) {
        isLoading = false;
      }
    }
  }

  /// Get status request from failure type
  StatusRequest _getStatusFromFailure(Failure failure) {
    if (failure is NetworkFailure) {
      return StatusRequest.offlineFailure;
    } else if (failure is ServerFailure) {
      return StatusRequest.serverFailure;
    } else {
      return StatusRequest.failure;
    }
  }

  /// Show success snackbar
  void showSuccessSnackbar(String message) {
    Get.snackbar(
      'Success',
      message,
      snackPosition: SnackPosition.bottom,
      backgroundColor: Colors.green,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
      icon: const Icon(Icons.check_circle, color: Colors.white),
    );
  }

  /// Show error snackbar
  void showErrorSnackbar(String message) {
    Get.snackbar(
      'Error',
      message,
      snackPosition: SnackPosition.bottom,
      backgroundColor: Colors.red,
      colorText: Colors.white,
      duration: const Duration(seconds: 4),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
      icon: const Icon(Icons.error, color: Colors.white),
    );
  }

  /// Show info snackbar
  void showInfoSnackbar(String message) {
    Get.snackbar(
      'Info',
      message,
      snackPosition: SnackPosition.bottom,
      backgroundColor: Colors.blue,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
      icon: const Icon(Icons.info, color: Colors.white),
    );
  }

  /// Show warning snackbar
  void showWarningSnackbar(String message) {
    Get.snackbar(
      'Warning',
      message,
      snackPosition: SnackPosition.bottom,
      backgroundColor: Colors.orange,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
      icon: const Icon(Icons.warning, color: Colors.white),
    );
  }

  /// Show loading dialog
  void showLoadingDialog({String? message}) {
    Get.dialog(
      AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(message ?? 'Loading...'),
          ],
        ),
      ),
      barrierDismissible: false,
    );
  }

  /// Hide loading dialog
  void hideLoadingDialog() {
    if (Get.isDialogOpen == true) {
      Get.back();
    }
  }

  /// Show confirmation dialog
  Future<bool> showConfirmationDialog({
    required String title,
    required String message,
    String confirmText = 'Confirm',
    String cancelText = 'Cancel',
  }) async {
    final result = await Get.dialog<bool>(
      AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text(cancelText),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            child: Text(confirmText),
          ),
        ],
      ),
    );
    
    return result ?? false;
  }

  /// Refresh data
  Future<void> refresh() async {
    // Override in child controllers
  }

  /// Reset controller state
  void resetState() {
    isLoading = false;
    error = null;
    statusRequest = StatusRequest.success;
  }

  @override
  void onClose() {
    // Clean up resources
    super.onClose();
  }
}

/// Base list controller for handling paginated data
abstract class BaseListController<T> extends BaseController {
  // List data
  final _items = <T>[].obs;
  List<T> get items => _items;

  // Pagination
  final _currentPage = 1.obs;
  int get currentPage => _currentPage.value;
  set currentPage(int value) => _currentPage.value = value;

  final _hasNextPage = false.obs;
  bool get hasNextPage => _hasNextPage.value;
  set hasNextPage(bool value) => _hasNextPage.value = value;

  final _isLoadingMore = false.obs;
  bool get isLoadingMore => _isLoadingMore.value;
  set isLoadingMore(bool value) => _isLoadingMore.value = value;

  // Search
  final _searchQuery = ''.obs;
  String get searchQuery => _searchQuery.value;
  set searchQuery(String value) => _searchQuery.value = value;

  /// Load initial data
  Future<void> loadData({bool refresh = false}) async {
    if (refresh) {
      currentPage = 1;
      _items.clear();
    }
    
    await fetchData();
  }

  /// Load more data for pagination
  Future<void> loadMore() async {
    if (isLoadingMore || !hasNextPage) return;
    
    isLoadingMore = true;
    currentPage++;
    
    await fetchData();
    
    isLoadingMore = false;
  }

  /// Search items
  Future<void> search(String query) async {
    searchQuery = query;
    currentPage = 1;
    _items.clear();
    
    await fetchData();
  }

  /// Abstract method to fetch data - implement in child controllers
  Future<void> fetchData();

  /// Add item to list
  void addItem(T item) {
    _items.add(item);
  }

  /// Update item in list
  void updateItem(int index, T item) {
    if (index >= 0 && index < _items.length) {
      _items[index] = item;
    }
  }

  /// Remove item from list
  void removeItem(int index) {
    if (index >= 0 && index < _items.length) {
      _items.removeAt(index);
    }
  }

  /// Clear all items
  void clearItems() {
    _items.clear();
    currentPage = 1;
    hasNextPage = false;
  }

  @override
  Future<void> refresh() async {
    await loadData(refresh: true);
  }

  @override
  void resetState() {
    super.resetState();
    clearItems();
    searchQuery = '';
  }
}
