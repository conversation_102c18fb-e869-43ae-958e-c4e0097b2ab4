import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:deewan/core/errors/exceptions.dart';

/// A generic client for making HTTP requests.
/// It throws a [ServerException] for non-successful status codes.
class ApiClient {
  final http.Client _client;
  final String _baseUrl = "https://api.yourapp.com/v1"; // Example base URL

  ApiClient({required http.Client client}) : _client = client;

  Future<Map<String, dynamic>> get(
    String endpoint, {
    Map<String, String>? headers,
  }) async {
    final response = await _client.get(
      Uri.parse('$_baseUrl/$endpoint'),
      headers: headers,
    );
    return _handleResponse(response);
  }

  Future<Map<String, dynamic>> post(
    String endpoint, {
    required Map<String, dynamic> body,
    Map<String, String>? headers,
  }) async {
    final response = await _client.post(
      Uri.parse('$_baseUrl/$endpoint'),
      headers: {'Content-Type': 'application/json', ...?headers},
      body: jsonEncode(body),
    );
    return _handleResponse(response);
  }

  // You can add put, delete, etc. in a similar fashion

  Map<String, dynamic> _handleResponse(http.Response response) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      if (response.body.isEmpty) {
        return {}; // Return empty map for empty responses (e.g., 204 No Content)
      }
      return jsonDecode(response.body) as Map<String, dynamic>;
    } else {
      // Throw a specific exception that the repository layer can catch.
      throw ServerException(
        message: 'Server error: ${response.reasonPhrase}',
        statusCode: response.statusCode,
      );
    }
  }
}
