import 'dart:io';

/// An abstract contract for checking network connectivity.
/// This allows for easy mocking in tests.
abstract class NetworkInfo {
  Future<bool> get isConnected;
}

/// The implementation of [NetworkInfo] that uses a real network check.
class NetworkInfoImpl implements NetworkInfo {
  // Using a dependency like 'internet_connection_checker' is more robust
  // than a simple lookup, but this implementation matches your original logic.
  @override
  Future<bool> get isConnected async {
    try {
      // Google's public DNS server is a reliable target for lookups.
      final result = await InternetAddress.lookup('*******');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }
}
