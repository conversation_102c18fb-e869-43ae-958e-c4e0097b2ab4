


abstract class WebSocketRepository {
  Future<bool> connect();
  Future<void> disconnect();
  Stream<WebSocketMessage> get messageStream;
  Future<bool> sendMessage(Map<String, dynamic> message);
  Future<bool> joinRoom(String roomId);
  Future<bool> leaveRoom(String roomId);
  bool get isConnected;
  Stream<ConnectionStatus> get connectionStatusStream;
  Future<void> reconnect();
  void subscribeToRoom(String roomId);
  void unsubscribeFromRoom(String roomId);
}
