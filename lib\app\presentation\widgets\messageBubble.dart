import 'package:deewan/app/data/models/entities/user_profile_model.dart';
import 'package:deewan/app/data/models/entities/message_models.dart';
import 'package:deewan/app/presentation/widgets/messageBuilder.dart';
import 'package:flutter/material.dart';

class MessageBubble extends StatelessWidget {
  final Message message;
  final MyIdentity currentAccount;
  final MyIdentity auther;

  const MessageBubble({
    super.key,
    required this.message,
    required this.currentAccount,
    required this.auther,
  });
  @override
  Widget build(BuildContext context) {
    bool isMe = message.autherId.toString() == currentAccount.id.toString();
    return Align(
      alignment: isMe ? Alignment.centerRight : Alignment.centerLeft,
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
        tileColor: isMe ? Colors.blue[200] : Colors.grey[400],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        leading: isMe
            ? null
            : CircleAvatar(
                backgroundImage: NetworkImage(auther.userImage),
              ),
        title: Expanded(
          child: BuildMessage(message),
        ),
      ),
    );
  }
}
