import 'dart:async';
import 'dart:typed_data';
import 'package:deewan/app/data/models/entities/message_models.dart';

abstract class MediaRepositoryInterface {
  Stream<List<ImageUrl>> watchImagesForEntity(String entityType, String entityId);
  Stream<List<VideoUrl>> watchVideosForEntity(String entityType, String entityId);
  Stream<List<AudioUrl>> watchAudiosForEntity(String entityType, String entityId);
  Stream<List<FileUrl>> watchFilesForEntity(String entityType, String entityId);
  
  Future<ImageUrl?> getImageById(String imageId);
  Future<VideoUrl?> getVideoById(String videoId);
  Future<AudioUrl?> getAudioById(String audioId);
  Future<FileUrl?> getFileById(String fileId);
  
  Future<List<ImageUrl>> getImagesForEntity(String entityType, String entityId);
  Future<List<VideoUrl>> getVideosForEntity(String entityType, String entityId);
  Future<List<AudioUrl>> getAudiosForEntity(String entityType, String entityId);
  Future<List<FileUrl>> getFilesForEntity(String entityType, String entityId);
  
  Future<ImageUrl> saveImage(Uint8List imageData, String fileName, 
      {String? entityType, String? entityId, String? description});
  Future<VideoUrl> saveVideo(Uint8List videoData, String fileName, 
      {String? entityType, String? entityId, String? description, int? durationMs});
  Future<AudioUrl> saveAudio(Uint8List audioData, String fileName, 
      {String? entityType, String? entityId, String? description, int? durationMs});
  Future<FileUrl> saveFile(Uint8List fileData, String fileName, 
      {String? entityType, String? entityId, String? description, String? mimeType});
  
  Future<void> deleteMedia(String mediaId, MediaType mediaType);
  Future<void> updateMediaDescription(String mediaId, MediaType mediaType, String description);
  
  Future<Uint8List?> getMediaData(String mediaUrl);
  Future<String> generateThumbnail(String videoId);
  Future<int> getMediaDuration(String mediaId, MediaType mediaType);
}