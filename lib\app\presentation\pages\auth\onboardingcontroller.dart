import 'package:flutter/material.dart';
import 'package:get/get.dart';

class OnboardingController extends GetxController {
  final ScrollController scrollController = ScrollController();
  Rx<String> buttontext = Rx<String>(" Scroll UP ↑↑↑");
  Rx<Color> buttonColor = Rx<Color>(const Color.fromARGB(255, 255, 153, 0));
  Rx<bool> isactive = Rx<bool>(false);
  //Rx<void> Function()? onPressed;
//⇪↑
  void scrolllistner() {
    if (scrollController.offset >=
            scrollController.position.maxScrollExtent - 20 &&
        !scrollController.position.outOfRange) {
      buttontext.value = "Get Started";
      isactive.value = true;
      buttonColor.value = const Color.fromARGB(255, 97, 171, 251);
    }
    if (scrollController.offset <=
            scrollController.position.maxScrollExtent - 20 &&
        !scrollController.position.outOfRange) {
      buttontext.value = "Read the Terms";
      buttonColor.value = const Color.fromARGB(255, 255, 153, 0);
      isactive.value = false;
    }
  }

  @override
  void onInit() {
    super.onInit();
    scrollController.addListener(scrolllistner);
  }
}
