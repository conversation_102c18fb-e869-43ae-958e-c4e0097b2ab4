import 'package:flutter/material.dart';

/// Color schemes for light and dark themes
class AppColorSchemes {
  // Private constructor to prevent instantiation
  AppColorSchemes._();

  /// Light color scheme
  static const ColorScheme lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: Color(0xFF1976D2), // Blue
    onPrimary: Color(0xFFFFFFFF), // White
    primaryContainer: Color(0xFFBBDEFB), // Light Blue
    onPrimaryContainer: Color(0xFF0D47A1), // Dark Blue
    secondary: Color(0xFF03DAC6), // Teal
    onSecondary: Color(0xFF000000), // Black
    secondaryContainer: Color(0xFFB2DFDB), // Light Teal
    onSecondaryContainer: Color(0xFF004D40), // Dark Teal
    tertiary: Color(0xFFFF9800), // Orange
    onTertiary: Color(0xFFFFFFFF), // White
    tertiaryContainer: Color(0xFFFFE0B2), // Light Orange
    onTertiaryContainer: Color(0xFFE65100), // Dark Orange
    error: Color(0xFFD32F2F), // Red
    onError: Color(0xFFFFFFFF), // White
    errorContainer: Color(0xFFFFCDD2), // Light Red
    onErrorContainer: Color(0xFFB71C1C), // Dark Red
    background: Color(0xFFFAFAFA), // Very Light Grey
    onBackground: Color(0xFF212121), // Dark Grey
    surface: Color(0xFFFFFFFF), // White
    onSurface: Color(0xFF212121), // Dark Grey
    surfaceVariant: Color(0xFFF5F5F5), // Light Grey
    onSurfaceVariant: Color(0xFF757575), // Medium Grey
    outline: Color(0xFFBDBDBD), // Light Grey
    outlineVariant: Color(0xFFE0E0E0), // Very Light Grey
    shadow: Color(0xFF000000), // Black
    scrim: Color(0xFF000000), // Black
    inverseSurface: Color(0xFF303030), // Dark Grey
    onInverseSurface: Color(0xFFFFFFFF), // White
    inversePrimary: Color(0xFF90CAF9), // Light Blue
    surfaceTint: Color(0xFF1976D2), // Blue
  );

  /// Dark color scheme
  static const ColorScheme darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: Color(0xFF90CAF9), // Light Blue
    onPrimary: Color(0xFF0D47A1), // Dark Blue
    primaryContainer: Color(0xFF1565C0), // Medium Blue
    onPrimaryContainer: Color(0xFFE3F2FD), // Very Light Blue
    secondary: Color(0xFF80CBC4), // Light Teal
    onSecondary: Color(0xFF004D40), // Dark Teal
    secondaryContainer: Color(0xFF00695C), // Medium Teal
    onSecondaryContainer: Color(0xFFE0F2F1), // Very Light Teal
    tertiary: Color(0xFFFFB74D), // Light Orange
    onTertiary: Color(0xFFE65100), // Dark Orange
    tertiaryContainer: Color(0xFFFF8F00), // Medium Orange
    onTertiaryContainer: Color(0xFFFFF3E0), // Very Light Orange
    error: Color(0xFFEF5350), // Light Red
    onError: Color(0xFFB71C1C), // Dark Red
    errorContainer: Color(0xFFC62828), // Medium Red
    onErrorContainer: Color(0xFFFFEBEE), // Very Light Red
    background: Color(0xFF121212), // Very Dark Grey
    onBackground: Color(0xFFE0E0E0), // Light Grey
    surface: Color(0xFF1E1E1E), // Dark Grey
    onSurface: Color(0xFFE0E0E0), // Light Grey
    surfaceVariant: Color(0xFF424242), // Medium Dark Grey
    onSurfaceVariant: Color(0xFFBDBDBD), // Light Grey
    outline: Color(0xFF757575), // Medium Grey
    outlineVariant: Color(0xFF424242), // Medium Dark Grey
    shadow: Color(0xFF000000), // Black
    scrim: Color(0xFF000000), // Black
    inverseSurface: Color(0xFFE0E0E0), // Light Grey
    onInverseSurface: Color(0xFF121212), // Very Dark Grey
    inversePrimary: Color(0xFF1976D2), // Blue
    surfaceTint: Color(0xFF90CAF9), // Light Blue
  );

  /// Custom colors for specific use cases
  static const Color success = Color(0xFF4CAF50); // Green
  static const Color warning = Color(0xFFFF9800); // Orange
  static const Color info = Color(0xFF2196F3); // Blue
  static const Color lightSuccess = Color(0xFFC8E6C9); // Light Green
  static const Color lightWarning = Color(0xFFFFE0B2); // Light Orange
  static const Color lightInfo = Color(0xFFBBDEFB); // Light Blue

  /// Gradient colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [Color(0xFF1976D2), Color(0xFF42A5F5)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [Color(0xFF03DAC6), Color(0xFF4DB6AC)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
}
