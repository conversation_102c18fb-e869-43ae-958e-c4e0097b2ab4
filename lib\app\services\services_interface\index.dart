/// Service Interfaces Export File
///
/// This file provides a single import point for all service interfaces
/// in the application. It promotes clean architecture by separating
/// interface definitions from implementations.

// Base service interfaces
export 'app_services_interface.dart' hide IServiceInitializer;

// Specific service interfaces
export 'objectbox_service_interface.dart';
export 'settings_service_interface.dart';
export 'localization_service_interface.dart';

// Service management interfaces
export 'service_initializer_interface.dart';
