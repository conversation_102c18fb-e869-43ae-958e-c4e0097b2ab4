lib/
├── app/
│   ├── bindings/              # Dependency Injection Bindings
│   │   ├── initial_binding.dart          # App-wide permanent dependencies
│   │   ├── auth_binding.dart             # Authentication related dependencies
│   │   ├── chat_binding.dart             # Chat/messaging dependencies
│   │   ├── room_binding.dart             # Room management dependencies
│   │   ├── contact_binding.dart          # Contact management dependencies
│   │   └── settings_binding.dart         # Settings dependencies
│   │
│   ├── controllers/           # GetX Controllers (UI Logic)
│   │   ├── auth/
│   │   │   ├── auth_controller.dart
│   │   │   ├── login_controller.dart
│   │   │   └── registration_controller.dart
│   │   ├── chat/
│   │   │   ├── chat_controller.dart
│   │   │   ├── message_input_controller.dart
│   │   │   └── media_controller.dart
│   │   ├── dashboard/
│   │   │   ├── dashboard_controller.dart
│   │   │   └── room_list_controller.dart
│   │   ├── room/
│   │   │   ├── room_controller.dart
│   │   │   ├── room_creation_controller.dart
│   │   │   └── participants_controller.dart
│   │   ├── contact/
│   │   │   ├── contact_controller.dart
│   │   │   └── contact_sync_controller.dart
│   │   ├── profile/
│   │   │   ├── profile_controller.dart
│   │   │   └── identity_controller.dart
│   │   └── settings/
│   │       ├── settings_controller.dart
│   │       └── theme_controller.dart
│   │
│   ├── routes/               # App Navigation
│   │   ├── app_pages.dart
│   │   └── app_routes.dart
│   │
│   └── middlewares/          # Route Middlewares
│       ├── auth_middleware.dart
│       └── onboarding_middleware.dart
│
├── core/                     # Core Functionality
│   ├── base/                 # Base Classes
│   │   ├── base_controller.dart
│   │   ├── base_repository.dart
│   │   ├── base_service.dart
│   │   └── base_view.dart
│   │
│   ├── constants/            # App Constants
│   │   ├── app_constants.dart
│   │   ├── api_constants.dart
│   │   ├── storage_keys.dart
│   │   └── message_constants.dart
│   │
│   ├── enums/               # Enums
│   │   ├── message_enums.dart
│   │   ├── room_enums.dart
│   │   ├── auth_enums.dart
│   │   └── app_enums.dart
│   │
│   ├── errors/              # Error Handling
│   │   ├── exceptions.dart
│   │   ├── failures.dart
│   │   └── error_handler.dart
│   │
│   ├── extensions/          # Dart Extensions
│   │   ├── string_extensions.dart
│   │   ├── datetime_extensions.dart
│   │   └── widget_extensions.dart
│   │
│   ├── network/             # Network Layer
│   │   ├── network_info.dart
│   │   ├── websocket_client.dart
│   │   └── api_interceptor.dart
│   │
│   ├── storage/             # Local Storage
│   │   ├── objectbox_store.dart
│   │   ├── secure_storage.dart
│   │   └── shared_preferences_helper.dart
│   │
│   ├── theme/               # App Theming
│   │   ├── app_theme.dart
│   │   ├── light_theme.dart
│   │   └── dark_theme.dart
│   │
│   └── utils/               # Utilities
│       ├── date_utils.dart
│       ├── file_utils.dart
│       ├── crypto_utils.dart
│       ├── validation_utils.dart
│       └── permission_utils.dart
│
├── data/                    # Data Layer
│   ├── datasources/         # Data Sources
│   │   ├── local/
│   │   │   ├── auth_local_datasource.dart
│   │   │   ├── message_local_datasource.dart
│   │   │   ├── room_local_datasource.dart
│   │   │   ├── contact_local_datasource.dart
│   │   │   └── user_local_datasource.dart
│   │   └── remote/
│   │       ├── auth_remote_datasource.dart
│   │       ├── message_remote_datasource.dart
│   │       ├── room_remote_datasource.dart
│   │       └── websocket_datasource.dart
│   │
│   ├── mappers/             # Data Mappers
│   │   ├── message_mapper.dart
│   │   ├── room_mapper.dart
│   │   ├── user_mapper.dart
│   │   └── contact_mapper.dart
│   │
│   ├── models/              # ObjectBox Entities
│   │   ├── entities/
│   │   │   ├── message_models.dart
│   │   │   ├── room_models.dart
│   │   │   ├── user_models.dart
│   │   │   ├── contact_models.dart
│   │   │   └── item_models.dart
│   │   └── dto/             # Data Transfer Objects
│   │       ├── websocket_dto.dart
│   │       ├── auth_dto.dart
│   │       └── sync_dto.dart
│   │
│   └── repositories/        # Repository Implementations
│       ├── auth_repository_impl.dart
│       ├── message_repository_impl.dart
│       ├── room_repository_impl.dart
│       ├── contact_repository_impl.dart
│       ├── file_repository_impl.dart
│       ├── websocket_repository_impl.dart
│       └── sync_repository_impl.dart
│
├── domain/                  # Domain Layer
│   ├── entities/            # Business Entities (if different from data models)
│   │   └── ... (optional clean architecture entities)
│   │
│   ├── repositories/        # Repository Interfaces
│   │   ├── auth_repository.dart
│   │   ├── message_repository.dart
│   │   ├── room_repository.dart
│   │   ├── contact_repository.dart
│   │   ├── file_repository.dart
│   │   ├── websocket_repository.dart
│   │   └── sync_repository.dart
│   │
│   └── usecases/           # Business Logic Use Cases (optional)
│       ├── auth/             #
│       ├── messaging/
│       └── rooms/
│
├── services/               # Application Services
│   ├── interfaces/         # Service Interfaces
│   │   ├── auth_service.dart
│   │   ├── message_service.dart
│   │   ├── room_service.dart
│   │   ├── notification_service.dart
│   │   ├── file_service.dart
│   │   ├── websocket_service.dart
│   │   ├── sync_service.dart
│   │   ├── offline_queue_service.dart
│   │   ├── encryption_service.dart
│   │   ├── cache_service.dart
│   │   └── network_service.dart
│   │
│   └── implementations/    # Service Implementations
│       ├── auth_service_impl.dart
│       ├── message_service_impl.dart
│       ├── room_service_impl.dart
│       ├── notification_service_impl.dart
│       ├── file_service_impl.dart
│       ├── websocket_service_impl.dart
│       ├── sync_service_impl.dart
│       ├── offline_queue_service_impl.dart
│       ├── encryption_service_impl.dart
│       ├── cache_service_impl.dart
│       └── network_service_impl.dart
│
├── presentation/           # UI Layer
│   ├── pages/              # App Screens
│   │   ├── auth/
│   │   │   ├── login_page.dart
│   │   │   ├── registration_page.dart
│   │   │   └── phone_verification_page.dart
│   │   ├── dashboard/
│   │   │   └── dashboard_page.dart
│   │   ├── chat/
│   │   │   ├── chat_page.dart
│   │   │   └── media_preview_page.dart
│   │   ├── rooms/
│   │   │   ├── room_list_page.dart
│   │   │   ├── room_info_page.dart
│   │   │   └── create_room_page.dart
│   │   ├── contacts/
│   │   │   ├── contacts_page.dart
│   │   │   └── contact_profile_page.dart
│   │   ├── profile/
│   │   │   ├── profile_page.dart
│   │   │   └── identity_management_page.dart
│   │   └── settings/
│   │       ├── settings_page.dart
│   │       ├── privacy_settings_page.dart
│   │       └── notification_settings_page.dart
│   │
│   └── widgets/            # Reusable Widgets
│       ├── common/
│       │   ├── loading_widget.dart
│       │   ├── error_widget.dart
│       │   ├── custom_app_bar.dart
│       │   └── custom_button.dart
│       ├── chat/
│       │   ├── message_bubble.dart
│       │   ├── message_input.dart
│       │   ├── media_message.dart
│       │   └── typing_indicator.dart
│       ├── room/
│       │   ├── room_tile.dart
│       │   ├── participant_tile.dart
│       │   └── room_header.dart
│       └── contact/
│           ├── contact_tile.dart
│           └── contact_avatar.dart
│
└── main.dart               # App Entry Point

# Additional Files:

├── objectbox-model.json    # ObjectBox Model
├── pubspec.yaml           # Dependencies
└── analysis_options.yaml # Linting Rules