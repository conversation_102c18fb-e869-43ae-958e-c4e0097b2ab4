import 'dart:convert';
import 'package:deewan/core/errors/exceptions.dart';

/// Abstract interface for secure storage operations
abstract class SecureStorage {
  Future<void> write(String key, String value);
  Future<String?> read(String key);
  Future<void> delete(String key);
  Future<void> deleteAll();
  Future<bool> containsKey(String key);
  Future<Map<String, String>> readAll();
}

/// Implementation of secure storage using in-memory storage
/// Note: For production apps, consider using flutter_secure_storage
/// or shared_preferences for persistent storage
class SecureStorageImpl implements SecureStorage {
  static final Map<String, String> _storage = <String, String>{};

  SecureStorageImpl();

  @override
  Future<void> write(String key, String value) async {
    try {
      _storage[key] = value;
    } catch (e) {
      throw CacheException(message: 'Failed to write to secure storage: $e');
    }
  }

  @override
  Future<String?> read(String key) async {
    try {
      return _storage[key];
    } catch (e) {
      throw CacheException(message: 'Failed to read from secure storage: $e');
    }
  }

  @override
  Future<void> delete(String key) async {
    try {
      _storage.remove(key);
    } catch (e) {
      throw CacheException(message: 'Failed to delete from secure storage: $e');
    }
  }

  @override
  Future<void> deleteAll() async {
    try {
      _storage.clear();
    } catch (e) {
      throw CacheException(message: 'Failed to clear secure storage: $e');
    }
  }

  @override
  Future<bool> containsKey(String key) async {
    try {
      return _storage.containsKey(key);
    } catch (e) {
      throw CacheException(
        message: 'Failed to check key in secure storage: $e',
      );
    }
  }

  @override
  Future<Map<String, String>> readAll() async {
    try {
      return Map<String, String>.from(_storage);
    } catch (e) {
      throw CacheException(
        message: 'Failed to read all from secure storage: $e',
      );
    }
  }

  /// Write JSON object to storage
  Future<void> writeJson(String key, Map<String, dynamic> value) async {
    await write(key, jsonEncode(value));
  }

  /// Read JSON object from storage
  Future<Map<String, dynamic>?> readJson(String key) async {
    final value = await read(key);
    if (value == null) return null;

    try {
      return jsonDecode(value) as Map<String, dynamic>;
    } catch (e) {
      throw CacheException(
        message: 'Failed to decode JSON from secure storage: $e',
      );
    }
  }

  /// Write list to storage
  Future<void> writeList(String key, List<String> value) async {
    await write(key, jsonEncode(value));
  }

  /// Read list from storage
  Future<List<String>?> readList(String key) async {
    final value = await read(key);
    if (value == null) return null;

    try {
      final decoded = jsonDecode(value);
      return List<String>.from(decoded);
    } catch (e) {
      throw CacheException(
        message: 'Failed to decode list from secure storage: $e',
      );
    }
  }

  /// Write boolean to storage
  Future<void> writeBool(String key, bool value) async {
    await write(key, value.toString());
  }

  /// Read boolean from storage
  Future<bool?> readBool(String key) async {
    final value = await read(key);
    if (value == null) return null;

    return value.toLowerCase() == 'true';
  }

  /// Write integer to storage
  Future<void> writeInt(String key, int value) async {
    await write(key, value.toString());
  }

  /// Read integer from storage
  Future<int?> readInt(String key) async {
    final value = await read(key);
    if (value == null) return null;

    try {
      return int.parse(value);
    } catch (e) {
      throw CacheException(
        message: 'Failed to parse integer from secure storage: $e',
      );
    }
  }

  /// Write double to storage
  Future<void> writeDouble(String key, double value) async {
    await write(key, value.toString());
  }

  /// Read double from storage
  Future<double?> readDouble(String key) async {
    final value = await read(key);
    if (value == null) return null;

    try {
      return double.parse(value);
    } catch (e) {
      throw CacheException(
        message: 'Failed to parse double from secure storage: $e',
      );
    }
  }
}
