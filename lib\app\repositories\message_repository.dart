import 'dart:async';
import 'package:deewan/core/utils/classes/enums.dart';
import 'package:deewan/app/data/models/entities/message_models.dart';


abstract class MessageRepository {
  Stream<List<Message>> getMessagesForRoom(String roomId);
  Stream<Message?> watchLastMessageInRoom(String roomId);
  Future<List<Message>> getMessagesInRoom(String roomId, {int limit = 50, int offset = 0});
  Future<void> saveMessage(Message message);
  Future<Message> sendMessage(Message message);
  Future<Message> editMessage(String messageId, String newContent);
  Future<bool> deleteMessage(String messageId);
  Future<bool> markMessageAsRead(String messageId);
  Future<List<Message>> searchMessages(String query, {String? roomId});//
  Future<bool> starMessage(String messageId);
  Future<bool> unstarMessage(String messageId);
  Future<List<Message>> getStarredMessages();
  Future<bool> forwardMessage(String messageId, List<String> roomIds);
  Future<Message?> getMessageById(String messageId);
  Future<List<Message>> getMediaMessages(String roomId, MediaType mediaType);

}


