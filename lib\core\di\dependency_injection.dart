import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:deewan/core/network/api_client.dart';
import 'package:deewan/core/network/network_info.dart';
import 'package:deewan/core/storage/secure_storage.dart';
import 'package:deewan/core/storage/objectbox_store.dart';

/// Dependency injection setup using GetX
class DependencyInjection {
  /// Initialize all dependencies
  static Future<void> init() async {
    // Core dependencies
    await _initCore();

    // Network dependencies
    await _initNetwork();

    // Storage dependencies
    await _initStorage();

    // Services dependencies
    await _initServices();

    // Repository dependencies
    await _initRepositories();

    // Controller dependencies
    await _initControllers();
  }

  /// Initialize core dependencies
  static Future<void> _initCore() async {
    // HTTP Client
    Get.lazyPut<http.Client>(() => http.Client(), fenix: true);

    // Network Info
    Get.lazyPut<NetworkInfo>(() => NetworkInfoImpl(), fenix: true);
  }

  /// Initialize network dependencies
  static Future<void> _initNetwork() async {
    // API Client
    Get.lazyPut<ApiClient>(
      () => ApiClient(client: Get.find<http.Client>()),
      fenix: true,
    );
  }

  /// Initialize storage dependencies
  static Future<void> _initStorage() async {
    // Secure Storage
    Get.lazyPut<SecureStorage>(() => SecureStorageImpl(), fenix: true);

    // ObjectBox Store
    Get.lazyPut<ObjectBoxStore>(() => ObjectBoxStore.instance, fenix: true);
  }

  /// Initialize services
  static Future<void> _initServices() async {
    // Add your services here
    // Example:
    // Get.lazyPut<AuthService>(() => AuthServiceImpl(), fenix: true);
  }

  /// Initialize repositories
  static Future<void> _initRepositories() async {
    // Add your repositories here
    // Example:
    // Get.lazyPut<AuthRepository>(
    //   () => AuthRepositoryImpl(
    //     apiClient: Get.find<ApiClient>(),
    //     secureStorage: Get.find<SecureStorage>(),
    //   ),
    //   fenix: true,
    // );
  }

  /// Initialize controllers
  static Future<void> _initControllers() async {
    // Add your controllers here
    // Example:
    // Get.lazyPut<AuthController>(
    //   () => AuthController(authRepository: Get.find<AuthRepository>()),
    //   fenix: true,
    // );
  }

  /// Clean up all dependencies
  static void dispose() {
    Get.reset();
  }
}
