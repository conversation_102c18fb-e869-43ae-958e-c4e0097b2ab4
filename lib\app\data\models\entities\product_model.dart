import 'package:deewan/app/data/models/entities/item_models.dart';
import 'package:objectbox/objectbox.dart';

@Entity()
class Product {
  @Id()
  final int id = 0;
  final String? productId;
  final String? productTitle;
  @Backlink('product')
  final ToOne<Item>? item = ToOne<Item>();
  final String? productStatus; // ready ongoing, cancelled, deleted ,
  final Item? createdBy;
  final DateTime?
      lastUpdate; // very important to know when the package was last updated
  final String? productDescription;
  final String? productCategory;
  final String? productBarcode;
  final String? productSku;
  final String? productManufacturer;
  final String? productModelNumber;
  final String? productSerialCode;
  final double? price;
  final int? count;
  final bool? isAvailable;
  final bool? isDeleted;
  final bool? isReady;
  final bool? isAdded;
  final double? discount;
  final String? productResult;

  Product( this.productId,
      this.productTitle,
      this.productStatus,
      this.createdBy,
      this.lastUpdate,
      this.productDescription,
      this.productCategory,
      this.productBarcode,
      this.productSku,
      this.productManufacturer,
      this.productModelNumber,
      this.productSerialCode,
      this.price,
      this.count,
      this.isAvailable,
      this.isDeleted,
      this.isReady, this.isAdded, this.discount, this.productResult,
);
}
