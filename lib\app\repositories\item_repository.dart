import 'dart:async';
import 'package:deewan/app/data/models/entities/item_models.dart';

abstract class ItemRepositoryInterface {
  Stream<List<Item>> watchItemsInList(String itemListId);
  Stream<List<ItemList>> watchUserItemLists(String userProfileId);
  Stream<Item?> watchItem(String itemId);
  
  Future<List<Item>> getItemsInList(String itemListId, {int limit = 50, int offset = 0});
  Future<Item?> getItemById(String itemId);
  Future<ItemList?> getItemListById(String itemListId);
  Future<List<ItemList>> getUserItemLists(String userProfileId);
  Future<void> saveItem(Item item);
  Future<void> updateItem(Item item);
  Future<void> deleteItem(String itemId);
  Future<void> addItemToList(String itemId, String itemListId);
  Future<void> removeItemFromList(String itemId, String itemListId);
  Future<ItemList> createItemList(String name, String userProfileId, ItemListType type);
  Future<void> updateItemList(ItemList itemList);
  Future<void> deleteItemList(String itemListId);
  Future<List<Item>> searchItems(String query, {String? itemListId, ItemType? itemType});
  Future<List<Item>> getItemsByType(ItemType itemType, {String? userProfileId});
}