abstract class PermissionRepository {
  Future<PermissionStatus> requestCameraPermission();
  Future<PermissionStatus> requestMicrophonePermission();
  Future<PermissionStatus> requestStoragePermission();
  Future<PermissionStatus> requestLocationPermission();
  Future<PermissionStatus> requestContactsPermission();
  Future<PermissionStatus> requestNotificationPermission();
  Future<Map<Permission, PermissionStatus>> requestMultiplePermissions(List<Permission> permissions);
  Future<bool> openAppSettings();
  Future<PermissionStatus> checkPermission(Permission permission);
  Stream<PermissionStatus> watchPermission(Permission permission);
}
