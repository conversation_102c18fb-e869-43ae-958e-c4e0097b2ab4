import 'package:deewan/core/localization/localization_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../controllers/sittings/settingscontroller.dart';


class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SettingsController>();
    final localizationService = Get.find<LocalizationService>();

    return Scaffold(
      appBar: AppBar(
        title: Text('settings'.tr),
        centerTitle: true,
      ),
      body: Obx(() => ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              // Language Section
              _buildSectionHeader('languageSettings'.tr),
              const SizedBox(height: 8),
              _buildLanguageCard(controller, localizationService),
              const SizedBox(height: 24),

              // Appearance Section
              _buildSectionHeader('appearance'.tr),
              const SizedBox(height: 8),
              _buildThemeCard(controller),
              const SizedBox(height: 24),

              // Notifications Section
              _buildSectionHeader('notifications'.tr),
              const SizedBox(height: 8),
              _buildNotificationCard(controller),
              const SizedBox(height: 24),

              // Advanced Section
              _buildSectionHeader('advanced'.tr),
              const SizedBox(height: 8),
              _buildAdvancedCard(controller),
              const SizedBox(height: 24),

              // About Section
              _buildSectionHeader('about'.tr),
              const SizedBox(height: 8),
              _buildAboutCard(),
            ],
          )),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(left: 4.0, bottom: 8.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: Colors.grey,
        ),
      ),
    );
  }

  Widget _buildLanguageCard(SettingsController controller, LocalizationService localizationService) {
    return Card(
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.language),
            title: Text('language'.tr),
            subtitle: Text(controller.currentLanguageName),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () => _showLanguageDialog(controller),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.format_textdirection_r_to_l),
            title: Text('textDirection'.tr),
            subtitle: Text(localizationService.isRTL ? 'rtl'.tr : 'ltr'.tr),
            trailing: Switch(
              value: localizationService.isRTL,
              onChanged: (value) => controller.toggleLanguage(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildThemeCard(SettingsController controller) {
    return Card(
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.palette),
            title: Text('theme'.tr),
            subtitle: Text(controller.isDarkMode ? 'darkMode'.tr : 'lightMode'.tr),
            trailing: Switch(
              value: controller.isDarkMode,
              onChanged: (value) => controller.changeThemeMode(value),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationCard(SettingsController controller) {
    return Card(
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.notifications),
            title: Text('pushNotifications'.tr),
            subtitle: Text('notificationSettings'.tr),
            trailing: Switch(
              value: controller.notificationsEnabled,
              onChanged: (value) => controller.changeNotificationSettings(value),
            ),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.work),
            title: Text('backgroundWorkEnabled'.tr),
            subtitle: Text('Background sync and updates'),
            trailing: Switch(
              value: controller.backgroundWorkEnabled,
              onChanged: (value) => controller.changeBackgroundWorkSettings(value),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdvancedCard(SettingsController controller) {
    return Card(
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.restore),
            title: Text('reset'.tr),
            subtitle: Text('Reset all settings to default'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () => _showResetDialog(controller),
          ),
        ],
      ),
    );
  }

  Widget _buildAboutCard() {
    return Card(
      child: Column(
        children: [
          ListTile(
            leading: const Icon(Icons.info),
            title: Text('aboutApp'.tr),
            subtitle: Text('appDescription'.tr),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () => _showAboutDialog(),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.privacy_tip),
            title: Text('privacyPolicy'.tr),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () => _showPrivacyPolicy(),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.description),
            title: Text('termsOfService'.tr),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () => _showTermsOfService(),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.contact_support),
            title: Text('contactUs'.tr),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () => _showContactDialog(),
          ),
        ],
      ),
    );
  }

  void _showLanguageDialog(SettingsController controller) {
    Get.dialog(
      AlertDialog(
        title: Text('selectLanguage'.tr),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: controller.availableLanguages.map((locale) {
            final isSelected = controller.currentLocale == locale;
            final languageName = controller.getLanguageDisplayName(locale);
            
            return ListTile(
              title: Text(languageName),
              leading: Radio<Locale>(
                value: locale,
                groupValue: controller.currentLocale,
                onChanged: (value) {
                  if (value != null) {
                    controller.changeLanguage(value);
                    Get.back();
                  }
                },
              ),
              trailing: isSelected ? const Icon(Icons.check, color: Colors.green) : null,
              onTap: () {
                controller.changeLanguage(locale);
                Get.back();
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('cancel'.tr),
          ),
        ],
      ),
    );
  }

  void _showResetDialog(SettingsController controller) {
    Get.dialog(
      AlertDialog(
        title: Text('confirm'.tr),
        content: Text('Are you sure you want to reset all settings to default?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('cancel'.tr),
          ),
          TextButton(
            onPressed: () {
              controller.resetSettings();
              Get.back();
            },
            child: Text('reset'.tr),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog() {
    Get.dialog(
      AlertDialog(
        title: Text('aboutApp'.tr),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('appName'.tr, style: const TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Text('appDescription'.tr),
            const SizedBox(height: 16),
            Text('version'.tr + ': 1.0.0'),
            Text('buildNumber'.tr + ': 1'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('close'.tr),
          ),
        ],
      ),
    );
  }

  void _showPrivacyPolicy() {
    // Navigate to privacy policy page or show dialog
    Get.snackbar(
      'info'.tr,
      'Privacy policy feature coming soon',
      snackPosition: SnackPosition.bottom,
    );
  }

  void _showTermsOfService() {
    // Navigate to terms of service page or show dialog
    Get.snackbar(
      'info'.tr,
      'Terms of service feature coming soon',
      snackPosition: SnackPosition.bottom,
    );
  }

  void _showContactDialog() {
    Get.dialog(
      AlertDialog(
        title: Text('contactUs'.tr),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Email: <EMAIL>'),
            const SizedBox(height: 8),
            Text('Website: www.deewan.app'),
            const SizedBox(height: 8),
            Text('Phone: +****************'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('close'.tr),
          ),
        ],
      ),
    );
  }
}
