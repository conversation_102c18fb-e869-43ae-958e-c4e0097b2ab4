abstract class SyncRepository {
  Future<void> syncAllData();
  Future<void> syncMessages();
  Future<void> syncRooms();
  Future<void> syncIdentities();
  Future<void> syncUserProfile();
  Stream<SyncStatus> get syncStatusStream;
  Future<void> forceSyncRoom(String roomId);
  Future<DateTime?> getLastSyncTime();
  Future<void> setLastSyncTime(DateTime timestamp);
  Future<List<PendingSyncItem>> getPendingSyncItems();
  Future<void> addPendingSyncItem(PendingSyncItem item);
  Future<void> removePendingSyncItem(String itemId);
  Future<bool> needsSync();
}
