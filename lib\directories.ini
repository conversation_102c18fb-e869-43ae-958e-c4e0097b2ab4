lib/
├── app/
│   ├── bindings/              # Dependency Injection Bindings
│   │   ├── initial_bindings.dart         # App-wide permanent dependencies
│   │   ├── auth_binding.dart             # Authentication related dependencies
│   │   ├── order_room_binding.dart       # Order room dependencies
│   │   ├── private_room_binding.dart     # Private room dependencies
│   │   ├── pub_room_binding.dart         # Public room dependencies
│   │   └── settings_binding.dart         # Settings dependencies
│   │
│   ├── controllers/           # GetX Controllers (UI Logic)
│   │   ├── auth/                         # Authentication controllers
│   │   ├── chat/                         # Chat controllers
│   │   ├── contact/                      # Contact controllers
│   │   ├── dashboard/                    # Dashboard controllers
│   │   ├── profile/                      # Profile controllers
│   │   ├── room/                         # Room controllers
│   │   └── sittings/                     # Settings controllers
│   │
│   ├── data/                 # Data Layer
│   │   ├── data_sources/     # Data Sources
│   │   │   ├── api_provider.dart         # API provider
│   │   │   ├── static.dart               # Static data
│   │   │   ├── local/                    # Local data sources
│   │   │   └── remote/                   # Remote data sources
│   │   ├── mappers/          # Data Mappers / conversion logic
|   |   |   ├── auth_mapper.dart           # Converts auth DTOs ↔ entities   
|   |   |   ├── message_mapper.dart        # Converts message DTOs ↔ entities
|   |   |   └── websocket_mapper.dart      # Converts WebSocket DTOs ↔ entities
│   │   └── models/   
│   │       ├── dto/                      # Data Transfer Objects / models of api call
│   │       │   ├── message_dto.dart      # MessageDTO, MessageListDTO    
│   │       │   └── auth_dto.dart         # # AuthLoginDTO, AuthRegisterDTO, AuthTokenDTO 
│   │       │   ├── room_dto.dart         # room DTO  
│   │       │   └── contact_dto.dart      # contact DTO  
│   │       │   └── websocket_dto.dart    # WebSocketMessageDTO, WebSocketEventDTO  
│   │       └── entities/                 # ui Data Models
│   │           ├── user_profile_models.dart #  user profile models
│   │           ├── identity_models.dart   # identity models
│   │           ├── item_models.dart       # item models
│   │           ├── message_models.dart    # message models
│   │           ├── media_models.dart      # media models
│   │           ├── auth_models.dart       # authentication models
│   │           └── product_models.dart    # product models

│   │           
│   ├── presentation/         # UI Layer
│   │   ├── pages/            # App Screens
│   │   │   ├── auth/                     # Authentication pages
│   │   │   ├── dashboard/                # Dashboard pages
│   │   │   ├── login/                    # Login pages
│   │   │   ├── profile/                  # Profile pages
│   │   │   ├── rooms/                    # Room pages
│   │   │   └── sittings/                 # Settings pages
│   │   └── widgets/          # Reusable Widgets
│   │       ├── func.txt                  # Widget functions documentation
│   │       ├── messageBubble.dart        # Message bubble widget
│   │       └── messageBuilder.dart       # Message builder widget
│   │
│   ├── repositories/         # Repository Layer
│   │   ├── auth_repository.dart          # Authentication repository
│   │   ├── contact_page_repository.dart  # Contact page repository
│   │   ├── contact_sync_repository.dart  # Contact sync repository
│   │   ├── identity_repository.dart      # Identity repository
│   │   ├── item_repository.dart          # Item repository
│   │   ├── media_repository.dart         # Media repository
│   │   ├── message_repository.dart       # Message repository
│   │   ├── product_repository.dart       # Product repository
│   │   ├── room_repository.dart          # Room repository
│   │   ├── user_profile_repository.dart  # User profile repository
│   │   ├── repositories_impl/            # Repository implementations
│   │   └── repositories_interface/       # Repository interfaces
│   │
│   ├── routes/               # App Navigation
│   │   ├── app_pages.dart
│   │   └── app_routes.dart
│   │
│   ├── services/             # Application Services
│   │   ├── services_impl/    # Service Implementations
│   │   │   └── appservices.dart          # App services implementation
│   │   └── services_interface/           # Service Interfaces
│   │
│   └── middlewares/          # Route Middlewares
│       ├── auth_middleware.dart
│       └── onboarding_middleware.dart
│
├── core/                     # Core Functionality
│   ├── assets/               # Asset Management
│   │   └── imageassets.dart              # Image asset definitions
│   │
│   ├── constant/             # App Constants
│   │   ├── api_constants.dart            # API related constants
│   │   ├── app_constants.dart            # General app constants
│   │   ├── message_constants.dart        # Message related constants
│   │   └── storage_keys.dart             # Storage key constants
│   │
│   ├── enums/               # Enums
│   │   └── enums.dart                    # All application enums
│   │
│   ├── errors/              # Error Handling
│   │   ├── error_handler.dart            # Error handler
│   │   ├── exceptions.dart               # Custom exceptions
│   │   └── failures.dart                 # Failure classes
│   │
│   ├── extensions/          # Dart Extensions
│   │   ├── datetime_extensions.dart      # DateTime extensions
│   │   └── widget_extensions.dart        # Widget extensions
│   │
│   ├── localization/        # Internationalization
│   │   └── translation.dart              # App translations
|   |   
│   │
│   ├── network/             # Network Layer
│   │   ├── api_client.dart               # HTTP API client
│   │   ├── api_interceptor.dart          # API interceptor
│   │   ├── network_info.dart             # Network connectivity
│   │   └── websocket_client.dart         # WebSocket client
│   │
│   ├── shared/              # Shared Components
│   │
│   ├── storage/             # Local Storage
│   │   ├── objectbox_store.dart          # ObjectBox store
│   │   └── secure_storage.dart           # Secure storage
│   │
│   ├── theme/               # App Theming
│   │   ├── app_theme.dart                # Main theme
│   │   ├── color_schemes.dart            # Color schemes
│   │   ├── custom_theme.dart             # Custom theme
│   │   ├── dark_theme.dart               # Dark theme
│   │   └── light_theme.dart              # Light theme
│   │
│   └── utils/               # Utilities
│       ├── crypto_utils.dart             # Cryptography utilities
│       ├── logger_utils.dart             # Logging utilities
│       ├── permission_utils.dart         # Permission utilities
│       └── validation_utils.dart         # Validation utilities
│
├── main.dart               # App Entry Point
├── orientation.txt         # App orientation documentation
└── structure.ini           # This file - project structure documentation

# Additional Files in Project Root:

├── objectbox-model.json    # ObjectBox Model (generated)
├── pubspec.yaml           # Dependencies
├── pubspec.lock           # Dependency lock file
├── analysis_options.yaml  # Linting Rules
├── README.md              # Project documentation
├── deewan.iml             # IntelliJ project file
├── android/               # Android platform files
├── ios/                   # iOS platform files
├── linux/                 # Linux platform files
├── macos/                 # macOS platform files
├── windows/               # Windows platform files
├── web/                   # Web platform files
├── test/                  # Test files
└── build/                 # Build output directory

# Notes:
# - This structure reflects the current state of the Deewan Flutter application
# - The app follows a hybrid architecture with GetX for state management
# - ObjectBox is used for local data persistence
# - The structure is organized by feature and layer for maintainability
# - Some planned features (domain layer, clean architecture) are not yet implemented
# - Missing files referenced in imports may need to be created:
#   * lib/core/utils/classes/objectbox.dart
#   * lib/core/utils/classes/enums.dart