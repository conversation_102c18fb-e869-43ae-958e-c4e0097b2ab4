import 'package:objectbox/objectbox.dart';

@Entity()
class Device {
  @Id()
  final String id;
  final String name;
  final String type;
  final String status;
  final String location;
  final String lastSeen;
  final String batteryLevel;
  final String signalStrength;
  final String ipAddress;
  final String macAddress;
  final String os;
  final String version;
  final String manufacturer;
  final String model;
  final String serialNumber;
  final String description;
  final String notes;
  final String createdAt;
  final String updatedAt;

  Device({
    required this.id,
    required this.name,
    required this.type,
    required this.status,
    required this.location,
    required this.lastSeen,
    required this.batteryLevel,
    required this.signalStrength,
    required this.ipAddress,
    required this.macAddress,
    required this.os,
    required this.version,
    required this.manufacturer,
    required this.model,
    required this.serialNumber,
    required this.description,
    required this.notes,
    required this.createdAt,
    required this.updatedAt,
  });
    
}
