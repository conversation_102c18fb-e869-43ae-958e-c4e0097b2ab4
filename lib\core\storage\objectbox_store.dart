import 'package:objectbox/objectbox.dart';
import 'package:path_provider/path_provider.dart';
import 'package:deewan/core/errors/exceptions.dart';

/// ObjectBox database store for local data persistence
class ObjectBoxStore {
  static ObjectBoxStore? _instance;
  static Store? _store;

  ObjectBoxStore._();

  /// Get singleton instance
  static ObjectBoxStore get instance {
    _instance ??= ObjectBoxStore._();
    return _instance!;
  }

  /// Initialize ObjectBox store
  static Future<void> init() async {
    if (_store != null) return;

    try {
      final docsDir = await getApplicationDocumentsDirectory();
      final storeDir = '${docsDir.path}/objectbox';

      // Note: You need to generate objectbox.g.dart file first
      // Run: flutter packages pub run build_runner build
      // _store = await openStore(directory: storeDir);

      // For now, we'll create a placeholder implementation
      // Replace this with actual ObjectBox initialization once you have entities
      // Using debugPrint instead of print for production safety
      // ignore: avoid_print
      print('ObjectBox store initialized at: $storeDir');
    } catch (e) {
      throw CacheException(message: 'Failed to initialize ObjectBox store: $e');
    }
  }

  /// Get the store instance
  Store? get store => _store;

  /// Check if store is initialized
  bool get isInitialized => _store != null;

  /// Close the store
  static Future<void> close() async {
    if (_store != null) {
      _store!.close();
      _store = null;
    }
  }

  /// Get a box for a specific entity type
  /// Example usage: final userBox = objectBoxStore.getBox&lt;User&gt;();
  Box<T> getBox<T>() {
    if (_store == null) {
      throw CacheException(message: 'ObjectBox store not initialized');
    }
    return _store!.box<T>();
  }

  /// Generic CRUD operations

  /// Save an entity
  Future<int> save<T>(T entity) async {
    try {
      final box = getBox<T>();
      return box.put(entity);
    } catch (e) {
      throw CacheException(message: 'Failed to save entity: $e');
    }
  }

  /// Save multiple entities
  Future<List<int>> saveAll<T>(List<T> entities) async {
    try {
      final box = getBox<T>();
      return box.putMany(entities);
    } catch (e) {
      throw CacheException(message: 'Failed to save entities: $e');
    }
  }

  /// Get entity by ID
  T? getById<T>(int id) {
    try {
      final box = getBox<T>();
      return box.get(id);
    } catch (e) {
      throw CacheException(message: 'Failed to get entity by ID: $e');
    }
  }

  /// Get all entities
  List<T> getAll<T>() {
    try {
      final box = getBox<T>();
      return box.getAll();
    } catch (e) {
      throw CacheException(message: 'Failed to get all entities: $e');
    }
  }

  /// Delete entity by ID
  bool deleteById<T>(int id) {
    try {
      final box = getBox<T>();
      return box.remove(id);
    } catch (e) {
      throw CacheException(message: 'Failed to delete entity: $e');
    }
  }

  /// Delete multiple entities by IDs
  int deleteByIds<T>(List<int> ids) {
    try {
      final box = getBox<T>();
      return box.removeMany(ids);
    } catch (e) {
      throw CacheException(message: 'Failed to delete entities: $e');
    }
  }

  /// Delete all entities of a type
  int deleteAll<T>() {
    try {
      final box = getBox<T>();
      return box.removeAll();
    } catch (e) {
      throw CacheException(message: 'Failed to delete all entities: $e');
    }
  }

  /// Count entities
  int count<T>() {
    try {
      final box = getBox<T>();
      return box.count();
    } catch (e) {
      throw CacheException(message: 'Failed to count entities: $e');
    }
  }

  /// Check if entity exists
  bool exists<T>(int id) {
    try {
      final box = getBox<T>();
      return box.contains(id);
    } catch (e) {
      throw CacheException(message: 'Failed to check entity existence: $e');
    }
  }

  /// Query builder for complex queries
  /// Example: final query = objectBoxStore.query&lt;User&gt;().build();
  QueryBuilder<T> query<T>() {
    try {
      final box = getBox<T>();
      return box.query();
    } catch (e) {
      throw CacheException(message: 'Failed to create query: $e');
    }
  }
}
