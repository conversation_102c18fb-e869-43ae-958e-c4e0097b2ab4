import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:deewan/core/errors/exceptions.dart';
import 'package:deewan/core/errors/failures.dart';
import 'package:deewan/core/constant/app_constants.dart';

/// Global error handler for the application
/// Converts exceptions to failures and provides user-friendly error messages
class ErrorHandler {
  /// Converts exceptions to failures with appropriate error messages
  static Failure handleException(Exception exception) {
    if (exception is ServerException) {
      return ServerFailure(
        message: _getServerErrorMessage(exception.statusCode),
        statusCode: exception.statusCode,
      );
    } else if (exception is CacheException) {
      return CacheFailure(
        message: exception.message ?? 'Failed to load cached data',
      );
    } else if (exception is NetworkException) {
      return NetworkFailure(
        message: exception.message ?? AppConstants.networkErrorMessage,
      );
    } else if (exception is ValidationException) {
      return ValidationFailure(
        message: exception.message,
        field: exception.field,
      );
    } else if (exception is TimeoutException) {
      return TimeoutFailure(message: AppConstants.timeoutErrorMessage);
    } else if (exception is SocketException) {
      return NetworkFailure(message: AppConstants.networkErrorMessage);
    } else if (exception is FormatException) {
      return DataParsingFailure(message: 'Invalid data format received');
    } else if (exception is UnauthorizedException) {
      return UnauthorizedFailure(message: 'Authentication required');
    } else if (exception is ForbiddenException) {
      return ForbiddenFailure(message: 'Access denied');
    } else {
      return UnknownFailure(message: AppConstants.genericErrorMessage);
    }
  }

  /// Handles errors in async operations and returns Either<Failure, T>
  static Future<Either<Failure, T>> handleAsyncOperation<T>(
    Future<T> Function() operation,
  ) async {
    try {
      final result = await operation();
      return Right(result);
    } catch (exception) {
      if (exception is Exception) {
        return Left(handleException(exception));
      } else {
        return Left(UnknownFailure(message: AppConstants.genericErrorMessage));
      }
    }
  }

  /// Handles synchronous operations and returns Either<Failure, T>
  static Either<Failure, T> handleSyncOperation<T>(T Function() operation) {
    try {
      final result = operation();
      return Right(result);
    } catch (exception) {
      if (exception is Exception) {
        return Left(handleException(exception));
      } else {
        return Left(UnknownFailure(message: AppConstants.genericErrorMessage));
      }
    }
  }

  /// Gets appropriate error message based on HTTP status code
  static String _getServerErrorMessage(int? statusCode) {
    switch (statusCode) {
      case 400:
        return 'Bad request. Please check your input.';
      case 401:
        return 'Authentication required. Please log in.';
      case 403:
        return 'Access denied. You don\'t have permission.';
      case 404:
        return 'Resource not found.';
      case 408:
        return 'Request timeout. Please try again.';
      case 409:
        return 'Conflict. The resource already exists.';
      case 422:
        return 'Invalid data provided.';
      case 429:
        return 'Too many requests. Please try again later.';
      case 500:
        return 'Internal server error. Please try again later.';
      case 502:
        return 'Bad gateway. Please try again later.';
      case 503:
        return 'Service unavailable. Please try again later.';
      case 504:
        return 'Gateway timeout. Please try again later.';
      default:
        return AppConstants.serverErrorMessage;
    }
  }

  /// Logs error details for debugging purposes
  static void logError(
    Exception exception, {
    StackTrace? stackTrace,
    Map<String, dynamic>? additionalData,
  }) {
    // In a real app, you would use a proper logging service like Firebase Crashlytics
    print('ERROR: ${exception.toString()}');
    if (stackTrace != null) {
      print('STACK TRACE: ${stackTrace.toString()}');
    }
    if (additionalData != null) {
      print('ADDITIONAL DATA: ${additionalData.toString()}');
    }
  }

  /// Determines if an error is recoverable
  static bool isRecoverableError(Failure failure) {
    return failure is NetworkFailure ||
        failure is TimeoutFailure ||
        failure is ServerFailure &&
            (failure.statusCode == null || failure.statusCode! >= 500);
  }

  /// Gets retry delay based on failure type
  static Duration getRetryDelay(Failure failure, int attemptNumber) {
    if (failure is NetworkFailure || failure is TimeoutFailure) {
      // Exponential backoff for network errors
      return Duration(seconds: (2 * attemptNumber).clamp(1, 30));
    } else if (failure is ServerFailure && failure.statusCode == 429) {
      // Rate limiting - longer delay
      return Duration(seconds: (5 * attemptNumber).clamp(5, 60));
    } else {
      return Duration(seconds: 5);
    }
  }

  /// Private constructor to prevent instantiation
  ErrorHandler._();
}
