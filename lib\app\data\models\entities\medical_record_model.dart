import 'package:deewan/app/data/models/entities/identity_model.dart';
import 'package:deewan/app/data/models/entities/item_models.dart';
//import 'package:deewan/data/models/product_model.dart';
import 'package:objectbox/objectbox.dart';

@Entity()
class MedicalRecord {
  @Id()
  final int id = 0;
  final String
  recordId; //lap or visit note or mhx or dx or sx or med or lab or referral or procedure or plan or instruction or report
  final String recordTitle;
  final Identity? identity;
  final Item itemId;
  final String
  type; // lap , visit note , mhx , shx , hpi , dx , sx , med , lab , referral , procedure , plan , instruction , report , etc
  final String?
  content; // content or result  depends on the type of medical records
  final String? status; //ready , ongoing , cancelled , deleted , archived
  final DateTime? addAt;
  final DateTime? updatedAt;
  final String description;
  @Backlink('medicalRecord')
  final ToOne<Item> item = ToOne<Item>();
  MedicalRecord(
    this.recordId,
    this.recordTitle,
    this.identity,
    this.itemId,
    this.updatedAt,
    this.status,
    this.addAt,
    this.description,
    this.type,
    this.content,
  );
}
