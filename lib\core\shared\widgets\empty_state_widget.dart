import 'package:flutter/material.dart';
import 'package:deewan/core/constant/app_constants.dart';
import 'package:deewan/core/shared/widgets/custom_button.dart';

/// Empty state widget for displaying when no data is available
class EmptyStateWidget extends StatelessWidget {
  final IconData? icon;
  final String? image;
  final String title;
  final String? subtitle;
  final String? actionText;
  final VoidCallback? onAction;
  final Color? iconColor;
  final double? iconSize;
  final EdgeInsetsGeometry? padding;

  const EmptyStateWidget({
    super.key,
    this.icon,
    this.image,
    required this.title,
    this.subtitle,
    this.actionText,
    this.onAction,
    this.iconColor,
    this.iconSize,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: padding ?? const EdgeInsets.all(AppConstants.largePadding),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon or Image
          if (icon != null)
            Icon(
              icon,
              size: iconSize ?? 80,
              color: iconColor ?? theme.colorScheme.outline,
            )
          else if (image != null)
            Image.asset(
              image!,
              width: iconSize ?? 80,
              height: iconSize ?? 80,
              color: iconColor ?? theme.colorScheme.outline,
            )
          else
            Icon(
              Icons.inbox_outlined,
              size: iconSize ?? 80,
              color: iconColor ?? theme.colorScheme.outline,
            ),

          const SizedBox(height: AppConstants.defaultPadding),

          // Title
          Text(
            title,
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
          ),

          // Subtitle
          if (subtitle != null) ...[
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              subtitle!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],

          // Action Button
          if (actionText != null && onAction != null) ...[
            const SizedBox(height: AppConstants.largePadding),
            CustomButton(
              text: actionText!,
              onPressed: onAction,
              type: ButtonType.primary,
              width: 200,
            ),
          ],
        ],
      ),
    );
  }
}

/// No data found widget
class NoDataFoundWidget extends StatelessWidget {
  final String? message;
  final VoidCallback? onRefresh;

  const NoDataFoundWidget({
    super.key,
    this.message,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.search_off,
      title: 'No Data Found',
      subtitle: message ?? 'No data available at the moment.',
      actionText: onRefresh != null ? 'Refresh' : null,
      onAction: onRefresh,
    );
  }
}

/// No search results widget
class NoSearchResultsWidget extends StatelessWidget {
  final String? searchQuery;
  final VoidCallback? onClearSearch;

  const NoSearchResultsWidget({
    super.key,
    this.searchQuery,
    this.onClearSearch,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.search_off,
      title: 'No Results Found',
      subtitle: searchQuery != null
          ? 'No results found for "$searchQuery".\nTry adjusting your search terms.'
          : 'No results found for your search.',
      actionText: onClearSearch != null ? 'Clear Search' : null,
      onAction: onClearSearch,
    );
  }
}

/// Network error widget
class NetworkErrorWidget extends StatelessWidget {
  final String? message;
  final VoidCallback? onRetry;

  const NetworkErrorWidget({
    super.key,
    this.message,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.wifi_off,
      title: 'Connection Error',
      subtitle: message ?? 'Please check your internet connection and try again.',
      actionText: onRetry != null ? 'Retry' : null,
      onAction: onRetry,
      iconColor: Theme.of(context).colorScheme.error,
    );
  }
}

/// Server error widget
class ServerErrorWidget extends StatelessWidget {
  final String? message;
  final VoidCallback? onRetry;

  const ServerErrorWidget({
    super.key,
    this.message,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.error_outline,
      title: 'Server Error',
      subtitle: message ?? 'Something went wrong on our end. Please try again later.',
      actionText: onRetry != null ? 'Retry' : null,
      onAction: onRetry,
      iconColor: Theme.of(context).colorScheme.error,
    );
  }
}

/// Under construction widget
class UnderConstructionWidget extends StatelessWidget {
  final String? message;

  const UnderConstructionWidget({
    super.key,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.construction,
      title: 'Under Construction',
      subtitle: message ?? 'This feature is currently under development.',
      iconColor: Theme.of(context).colorScheme.primary,
    );
  }
}

/// Coming soon widget
class ComingSoonWidget extends StatelessWidget {
  final String? message;

  const ComingSoonWidget({
    super.key,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.schedule,
      title: 'Coming Soon',
      subtitle: message ?? 'This feature will be available soon.',
      iconColor: Theme.of(context).colorScheme.primary,
    );
  }
}

/// Maintenance widget
class MaintenanceWidget extends StatelessWidget {
  final String? message;

  const MaintenanceWidget({
    super.key,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.build,
      title: 'Under Maintenance',
      subtitle: message ?? 'We are currently performing maintenance. Please try again later.',
      iconColor: Theme.of(context).colorScheme.primary,
    );
  }
}
