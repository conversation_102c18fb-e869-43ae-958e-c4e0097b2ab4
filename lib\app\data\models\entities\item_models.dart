import 'package:deewan/app/data/models/entities/address_model.dart';
import 'package:deewan/app/data/models/entities/identity_model.dart';
import 'package:deewan/app/data/models/entities/medical_record_model.dart';
import 'package:deewan/app/data/models/entities/product_model.dart';
import 'package:objectbox/objectbox.dart';

@Entity()
class Item {
  //listtaile view
  // product , contact card ,address ,msg ,,lap med
  @Id()
  final int id = 0;
  final String? itemTitle;
  final ToOne<Address>? address = ToOne<Address>(); //
  final ToOne<Identity>? identity = ToOne<Identity>(); // id cards
  final ToOne<Product>? product = ToOne<Product>(); //
  final ToOne<MedicalRecord>? medicalRecord = ToOne<MedicalRecord>(); // lap or med records
  //final ToOne<LinkPreview>? linkPreview = ToOne<LinkPreview>();
  @Backlink('item')
  final ToMany<ItemJoin>? itemJoins = ToMany<ItemJoin>();
  Item({this.itemTitle});
}

@Entity()
class ItemJoin {
  @Id()
  final int id = 0;
  final ToOne<Item> item = ToOne<Item>(); // join attribute
  final ToOne<ItemList> itemlist = ToOne<ItemList>(); // join attribute
  final Identity addedBy;
  final DateTime addedAt;
   final int? quantity;
   final String? statusInList ;
   final String? note ;
   
  ItemJoin(
    this.addedBy,
    this.addedAt, 
    {this.statusInList, this.note, this.quantity}
  );
}

@Entity()
class ItemList {
  //the package class
  @Id()
  final int id = 0;
  final String itemListID;
  final String? title;
  final int? status; // ready ongoing, cancelled, deleted ,
  final String? description; //, cutions , glass , fresh , hot,list of permittes
  final Identity? createdBy;
  final DateTime? createdAt;
  final DateTime? lastUpdate;
  final bool? isFulfilled;
  final Identity? fulfilledBy;
  @Backlink('itemList')
  final ToMany<ItemJoin>? itemJoins = ToMany<ItemJoin>();
  final int? itemsCount;
  final double? total; // price , results ,
  final int type;
  final List<String>? tags = [];
       //type of list ,should be a unified type of items : ID_list or shopping_cart or address or Saved_messages or medical_records
  // @Backlink('store')
  // final ToOne<ContactPage>? contactPage = ToOne<ContactPage>();
  // final ToOne<Room>? orderRoom = ToOne<Room>();
   //accessScope (String/enum): (e.g., "PRIVATE", "SHARED_READ_ONLY", "SHARED_READ_WRITE", "ORGANIZATION_VISIBLE", "PUBLIC"). Controls visibility and collaboration
  //version (int): For optimistic locking or tracking changes if needed.
  //isTemplate (bool): Indicates if this list can be used as a template to create new lists.

  //final ToOne<MyIdentity>? myIdentity = ToOne<MyIdentity>();
  ItemList(
       this.isFulfilled,
      this.createdAt,
      this.itemsCount,
      this.total,
      this.type,
      this.title,
      this.itemListID,
      this.status,
      this.createdBy,
      this.lastUpdate,
      this.fulfilledBy,
      this.description);
}
