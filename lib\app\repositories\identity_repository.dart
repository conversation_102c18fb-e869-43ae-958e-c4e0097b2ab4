// repositories/abstracts/identity_repository_interface.dart
import 'dart:async';
import 'package:deewan/app/data/models/entities/identity_model.dart';
import 'package:deewan/core/utils/classes/enums.dart'
import 'package:deewan/app/data/models/entities/item_models.dart';
import 'dart:async';
import 'package:deewan/app/data/models/entities/identity_model.dart';
import 'package:deewan/app/data/models/entities/user_profile_model.dart';
import 'package:deewan/app/data/models/entities/item_models.dart';
import 'package:objectbox/objectbox.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';


abstract class IdentityRepositoryInterface {
  Stream<List<Identity>> watchAllIdentities();
  Stream<List<MyIdentity>> watchMyIdentities(String userProfileId);
  Stream<Identity?> watchIdentity(String identityId);
  Stream<List<Identity>> watchContactsForIdentity(String myIdentityId);
  Stream<List<Identity>> watchBlockedContactsForIdentity(String myIdentityId);
  
  Future<Identity?> getIdentityById(String identityId);
  Future<List<Identity>> getAllIdentities();
  Future<List<MyIdentity>> getMyIdentities(String userProfileId);
  Future<MyIdentity?> getDefaultMyIdentity(String userProfileId);
  Future<MyIdentity?> getMyIdentityById(String myIdentityId);
  
  Future<Identity> createIdentity({
    required String identityId,
    required String name,
    required String phoneNumber,
    required String email,
    IdentityStatus status = IdentityStatus.offline,
    String? type,
  });
  
  Future<MyIdentity> createMyIdentity({
    required String userProfileId,
    required String identityId,
    String? userName,
    String? title,
    bool isDefault = false,
  });
  
  Future<void> updateIdentity(Identity identity);
  Future<void> updateMyIdentity(MyIdentity myIdentity);
  Future<void> updateIdentityStatus(String identityId, IdentityStatus status);
  Future<void> setDefaultMyIdentity(String userProfileId, String myIdentityId);
  
  Future<void> addToContacts(String myIdentityId, String contactIdentityId);
  Future<void> removeFromContacts(String myIdentityId, String contactIdentityId);
  Future<void> addToBlockedContacts(String myIdentityId, String blockedIdentityId);
  Future<void> removeFromBlockedContacts(String myIdentityId, String blockedIdentityId);
  
  Future<bool> isInContacts(String myIdentityId, String contactIdentityId);
  Future<bool> isBlocked(String myIdentityId, String blockedIdentityId);
  
  Future<List<Identity>> searchIdentities(String query);
  Future<List<Identity>> getContactsForMyIdentity(String myIdentityId);
  Future<List<Identity>> getBlockedContactsForMyIdentity(String myIdentityId);
  
  Future<void> deleteIdentity(String identityId);
  Future<void> deleteMyIdentity(String myIdentityId);
}


class IdentityRepository implements IdentityRepositoryInterface {
  final Store _store;
  late final Box<Identity> _identityBox;
  late final Box<MyIdentity> _myIdentityBox;
  late final Box<UserProfile> _userProfileBox;
  late final Box<Item> _itemBox;
  late final Box<ItemList> _itemListBox;
  late final Box<ItemJoin> _itemJoinBox;
  
  static const _uuid = Uuid();

  IdentityRepository(this._store) {
    _identityBox = _store.box<Identity>();
    _myIdentityBox = _store.box<MyIdentity>();
    _userProfileBox = _store.box<UserProfile>();
    _itemBox = _store.box<Item>();
    _itemListBox = _store.box<ItemList>();
    _itemJoinBox = _store.box<ItemJoin>();
  }

  @override
  Stream<List<Identity>> watchAllIdentities() {
    try {
      final query = _identityBox
          .query()
          .order(Identity_.name)
          .build();
      
      return query.watch(triggerImmediately: true).map((query) => query.find());
    } catch (e) {
      Get.printError(info: 'Error watching all identities: $e');
      return Stream.value([]);
    }
  }

  @override
  Stream<List<MyIdentity>> watchMyIdentities(String userProfileId) {
    try {
      final userProfileQuery = _userProfileBox
          .query(UserProfile_.userProfileId.equals(userProfileId))
          .build();
      final userProfile = userProfileQuery.findFirst();
      userProfileQuery.close();
      
      if (userProfile == null) return Stream.value([]);
      
      final query = _myIdentityBox
          .query(MyIdentity_.userProfile.equals(userProfile.id))
          .order(MyIdentity_.createdAt, flags: Order.descending)
          .build();
      
      return query.watch(triggerImmediately: true).map((query) => query.find());
    } catch (e) {
      Get.printError(info: 'Error watching my identities for user $userProfileId: $e');
      return Stream.value([]);
    }
  }

  @override
  Stream<Identity?> watchIdentity(String identityId) {
    try {
      final query = _identityBox
          .query(Identity_.identityId.equals(identityId))
          .build();
      
      return query.watch(triggerImmediately: true)
          .map((query) => query.findFirst());
    } catch (e) {
      Get.printError(info: 'Error watching identity $identityId: $e');
      return Stream.value(null);
    }
  }

  @override
  Stream<List<Identity>> watchContactsForIdentity(String myIdentityId) {
    try {
      final myIdentityQuery = _myIdentityBox
          .query(MyIdentity_.identity.equals(_getIdentityIdFromMyIdentity(myIdentityId)))
          .build();
      final myIdentity = myIdentityQuery.findFirst();
      myIdentityQuery.close();
      
      if (myIdentity == null) return Stream.value([]);
      
      final contactsList = myIdentity.contacts.target;
      if (contactsList == null) return Stream.value([]);
      
      final itemJoinQuery = _itemJoinBox
          .query(ItemJoin_.itemList.equals(contactsList.id))
          .build();
      
      return itemJoinQuery.watch(triggerImmediately: true).map((query) {
        final itemJoins = query.find();
        final identities = <Identity>[];
        
        for (final join in itemJoins) {
          final item = join.item.target;
          if (item?.itemType == ItemType.contact) {
            final identity = item?.relatedIdentity.target;
            if (identity != null) {
              identities.add(identity);
            }
          }
        }
        
        return identities;
      });
    } catch (e) {
      Get.printError(info: 'Error watching contacts for identity $myIdentityId: $e');
      return Stream.value([]);
    }
  }

  @override
  Stream<List<Identity>> watchBlockedContactsForIdentity(String myIdentityId) {
    try {
      final myIdentityQuery = _myIdentityBox
          .query(MyIdentity_.identity.equals(_getIdentityIdFromMyIdentity(myIdentityId)))
          .build();
      final myIdentity = myIdentityQuery.findFirst();
      myIdentityQuery.close();
      
      if (myIdentity == null) return Stream.value([]);
      
      final blockedList = myIdentity.blockedContacts.target;
      if (blockedList == null) return Stream.value([]);
      
      final itemJoinQuery = _itemJoinBox
          .query(ItemJoin_.itemList.equals(blockedList.id))
          .build();
      
      return itemJoinQuery.watch(triggerImmediately: true).map((query) {
        final itemJoins = query.find();
        final identities = <Identity>[];
        
        for (final join in itemJoins) {
          final item = join.item.target;
          if (item?.itemType == ItemType.contact) {
            final identity = item?.relatedIdentity.target;
            if (identity != null) {
              identities.add(identity);
            }
          }
        }
        
        return identities;
      });
    } catch (e) {
      Get.printError(info: 'Error watching blocked contacts for identity $myIdentityId: $e');
      return Stream.value([]);
    }
  }

  @override
  Future<Identity?> getIdentityById(String identityId) async {
    try {
      final query = _identityBox
          .query(Identity_.identityId.equals(identityId))
          .build();
      final identity = query.findFirst();
      query.close();
      return identity;
    } catch (e) {
      Get.printError(info: 'Error getting identity by id $identityId: $e');
      return null;
    }
  }

  @override
  Future<List<Identity>> getAllIdentities() async {
    try {
      final query = _identityBox
          .query()
          .order(Identity_.name)
          .build();
      final identities = query.find();
      query.close();
      return identities;
    } catch (e) {
      Get.printError(info: 'Error getting all identities: $e');
      return [];
    }
  }

  @override
  Future<List<MyIdentity>> getMyIdentities(String userProfileId) async {
    try {
      final userProfileQuery = _userProfileBox
          .query(UserProfile_.userProfileId.equals(userProfileId))
          .build();
      final userProfile = userProfileQuery.findFirst();
      userProfileQuery.close();
      
      if (userProfile == null) return [];
      
      final query = _myIdentityBox
          .query(MyIdentity_.userProfile.equals(userProfile.id))
          .order(MyIdentity_.createdAt, flags: Order.descending)
          .build();
      
      final myIdentities = query.find();
      query.close();
      return myIdentities;
    } catch (e) {
      Get.printError(info: 'Error getting my identities for user $userProfileId: $e');
      return [];
    }
  }

  @override
  Future<MyIdentity?> getDefaultMyIdentity(String userProfileId) async {
    try {
      final userProfileQuery = _userProfileBox
          .query(UserProfile_.userProfileId.equals(userProfileId))
          .build();
      final userProfile = userProfileQuery.findFirst();
      userProfileQuery.close();
      
      if (userProfile == null) return null;
      
      final query = _myIdentityBox
          .query(MyIdentity_.userProfile.equals(userProfile.id)
              .and(MyIdentity_.isDefault.equals(true)))
          .build();
      
      final defaultIdentity = query.findFirst();
      query.close();
      return defaultIdentity;
    } catch (e) {
      Get.printError(info: 'Error getting default identity for user $userProfileId: $e');
      return null;
    }
  }

  @override
  Future<MyIdentity?> getMyIdentityById(String myIdentityId) async {
    try {
      final identityQuery = _identityBox
          .query(Identity_.identityId.equals(myIdentityId))
          .build();
      final identity = identityQuery.findFirst();
      identityQuery.close();
      
      if (identity == null) return null;
      
      final query = _myIdentityBox
          .query(MyIdentity_.identity.equals(identity.id))
          .build();
      
      final myIdentity = query.findFirst();
      query.close();
      return myIdentity;
    } catch (e) {
      Get.printError(info: 'Error getting my identity by id $myIdentityId: $e');
      return null;
    }
  }

  @override
  Future<Identity> createIdentity({
    required String identityId,
    required String name,
    required String phoneNumber,
    required String email,
    IdentityStatus status = IdentityStatus.offline,
    String? type,
  }) async {
    try {
      return await _store.runInTransactionAsync(TxMode.write, () {
        final identity = Identity(
          identityId: identityId,
          name: name,
          phoneNumber: phoneNumber,
          email: email,
          lastSeen: DateTime.now(),
          createdAt: DateTime.now(),
          statusInt: status.index,
          type: type,
        );
        
        _identityBox.put(identity);
        return identity;
      });
    } catch (e) {
      Get.printError(info: 'Error creating identity: $e');
      rethrow;
    }
  }

  @override
  Future<MyIdentity> createMyIdentity({
    required String userProfileId,
    required String identityId,
    String? userName,
    String? title,
    bool isDefault = false,
  }) async {
    try {
      return await _store.runInTransactionAsync(TxMode.write, () {
        final userProfileQuery = _userProfileBox
            .query(UserProfile_.userProfileId.equals(userProfileId))
            .build();
        final userProfile = userProfileQuery.findFirst();
        userProfileQuery.close();
        
        if (userProfile == null) {
          throw Exception('User profile not found');
        }
        
        final identityQuery = _identityBox
            .query(Identity_.identityId.equals(identityId))
            .build();
        final identity = identityQuery.findFirst();
        identityQuery.close();
        
        if (identity == null) {
          throw Exception('Identity not found');
        }
        
        // If this should be default, unset other defaults
        if (isDefault) {
          final existingDefaultQuery = _myIdentityBox
              .query(MyIdentity_.userProfile.equals(userProfile.id)
                  .and(MyIdentity_.isDefault.equals(true)))
              .build();
          final existingDefaults = existingDefaultQuery.find();
          existingDefaultQuery.close();
          
          for (final existing in existingDefaults) {
            final updated = MyIdentity(
              userName: existing.userName,
              title: existing.title,
              isDefault: false,
              createdAt: existing.createdAt,
            );
            updated.id = existing.id;
            updated.identity.target = existing.identity.target;
            updated.userProfile.target = existing.userProfile.target;
            _myIdentityBox.put(updated);
          }
        }
        
        final myIdentity = MyIdentity(
          userName: userName,
          title: title,
          isDefault: isDefault,
          createdAt: DateTime.now(),
        );
        
        myIdentity.identity.target = identity;
        myIdentity.userProfile.target = userProfile;
        
        // Create default item lists
        _createDefaultItemLists(myIdentity);
        
        _myIdentityBox.put(myIdentity);
        return myIdentity;
      });
    } catch (e) {
      Get.printError(info: 'Error creating my identity: $e');
      rethrow;
    }
  }

  @override
  Future<void> updateIdentity(Identity identity) async {
    try {
      await _store.runInTransactionAsync(TxMode.write, () {
        _identityBox.put(identity);
      });
    } catch (e) {
      Get.printError(info: 'Error updating identity: $e');
      rethrow;
    }
  }

  @override
  Future<void> updateMyIdentity(MyIdentity myIdentity) async {
    try {
      await _store.runInTransactionAsync(TxMode.write, () {
        _myIdentityBox.put(myIdentity);
      });
    } catch (e) {
      Get.printError(info: 'Error updating my identity: $e');
      rethrow;
    }
  }

  @override
  Future<void> updateIdentityStatus(String identityId, IdentityStatus status) async {
    try {
      await _store.runInTransactionAsync(TxMode.write, () {
        final query = _identityBox
            .query(Identity_.identityId.equals(identityId))
            .build();
        final identity = query.findFirst();
        query.close();
        
        if (identity != null) {
          final updated = Identity(
            identityId: identity.identityId,
            name: identity.name,
            phoneNumber: identity.phoneNumber,
            email: identity.email,
            lastSeen: DateTime.now(),
            createdAt: identity.createdAt,
            statusInt: status.index,
            type: identity.type,
          );
          updated.id = identity.id;
          _identityBox.put(updated);
        }
      });
    } catch (e) {
      Get.printError(info: 'Error updating identity status: $e');
      rethrow;
    }
  }

  @override
  Future<void> setDefaultMyIdentity(String userProfileId, String myIdentityId) async {
    try {
      await _store.runInTransactionAsync(TxMode.write, () {
        final userProfileQuery = _userProfileBox
            .query(UserProfile_.userProfileId.equals(userProfileId))
            .build();
        final userProfile = userProfileQuery.findFirst();
        userProfileQuery.close();
        
        if (userProfile == null) return;
        
        final identityQuery = _identityBox
            .query(Identity_.identityId.equals(myIdentityId))
            .build();
        final identity = identityQuery.findFirst();
        identityQuery.close();
        
        if (identity == null) return;
        
        // Unset all defaults for this user
        final allMyIdentitiesQuery = _myIdentityBox
            .query(MyIdentity_.userProfile.equals(userProfile.id))
            .build();
        final allMyIdentities = allMyIdentitiesQuery.find();
        allMyIdentitiesQuery.close();
        
        for (final myIdentity in allMyIdentities) {
          final isNewDefault = myIdentity.identity.target?.identityId == myIdentityId;
          
          final updated = MyIdentity(
            userName: myIdentity.userName,
            title: myIdentity.title,
            isDefault: isNewDefault,
            createdAt: myIdentity.createdAt,
          );
          updated.id = myIdentity.id;
          updated.identity.target = myIdentity.identity.target;
          updated.userProfile.target = myIdentity.userProfile.target;
          
          _myIdentityBox.put(updated);
        }
      });
    } catch (e) {
      Get.printError(info: 'Error setting default my identity: $e');
      rethrow;
    }
  }

  @override
  Future<void> addToContacts(String myIdentityId, String contactIdentityId) async {
    try {
      await _store.runInTransactionAsync(TxMode.write, () {
        final myIdentity = _getMyIdentityByIdentityId(myIdentityId);
        final contactIdentity = _getIdentityById(contactIdentityId);
        
        if (myIdentity == null || contactIdentity == null) return;
        
        final contactsList = myIdentity.contacts.target ?? _createItemList('Contacts');
        myIdentity.contacts.target = contactsList;
        _myIdentityBox.put(myIdentity);
        
        // Create contact item
        final contactItem = Item(
          itemId: _uuid.v4(),
          title: contactIdentity.name,
          itemTypeInt: ItemType.contact.index,
          createdAt: DateTime.now(),
        );
        contactItem.relatedIdentity.target = contactIdentity;
        _itemBox.put(contactItem);
        
        // Create join
        final itemJoin = ItemJoin(
          addedAt: DateTime.now(),
          quantity: 1,
        );
        itemJoin.item.target = contactItem;
        itemJoin.itemList.target = contactsList;
        _itemJoinBox.put(itemJoin);
      });
    } catch (e) {
      Get.printError(info: 'Error adding to contacts: $e');
      rethrow;
    }
  }

  @override
  Future<void> removeFromContacts(String myIdentityId, String contactIdentityId) async {
    try {
      await _store.runInTransactionAsync(TxMode.write, () {
        final myIdentity = _getMyIdentityByIdentityId(myIdentityId);
        if (myIdentity == null) return;
        
        final contactsList = myIdentity.contacts.target;
        if (contactsList == null) return;
        
        _removeIdentityFromItemList(contactsList.id, contactIdentityId);
      });
    } catch (e) {
      Get.printError(info: 'Error removing from contacts: $e');
      rethrow;
    }
  }

  @override
  Future<void> addToBlockedContacts(String myIdentityId, String blockedIdentityId) async {
    try {
      await _store.runInTransactionAsync(TxMode.write, () {
        final myIdentity = _getMyIdentityByIdentityId(myIdentityId);
        final blockedIdentity = _getIdentityById(blockedIdentityId);
        
        if (myIdentity == null || blockedIdentity == null) return;
        
        final blockedList = myIdentity.blockedContacts.target ?? _createItemList('Blocked Contacts');
        myIdentity.blockedContacts.target = blockedList;
        _myIdentityBox.put(myIdentity);
        
        // Create blocked contact item
        final blockedItem = Item(
          itemId: _uuid.v4(),
          title: blockedIdentity.name,
          itemTypeInt: ItemType.contact.index,
          createdAt: DateTime.now(),
        );
        blockedItem.relatedIdentity.target = blockedIdentity;
        _itemBox.put(blockedItem);
        
        // Create join
        final itemJoin = ItemJoin(
          addedAt: DateTime.now(),
          quantity: 1,
        );
        itemJoin.item.target = blockedItem;
        itemJoin.itemList.target = blockedList;
        _itemJoinBox.put(itemJoin);
      });
    } catch (e) {
      Get.printError(info: 'Error adding to blocked contacts: $e');
      rethrow;
    }
  }

  @override
  Future<void> removeFromBlockedContacts(String myIdentityId, String blockedIdentityId) async {
    try {
      await _store.runInTransactionAsync(TxMode.write, () {
        final myIdentity = _getMyIdentityByIdentityId(myIdentityId);
        if (myIdentity == null) return;
        
        final blockedList = myIdentity.blockedContacts.target;
        if (blockedList == null) return;
        
        _removeIdentityFromItemList(blockedList.id, blockedIdentityId);
      });
    } catch (e) {
      Get.printError(info: 'Error removing from blocked contacts: $e');
      rethrow;
    }
  }

  @override
  Future<bool> isInContacts(String myIdentityId, String contactIdentityId) async {
    try {
      final myIdentity = _getMyIdentityByIdentityId(myIdentityId);
      if (myIdentity == null) return false;
      
      final contactsList = myIdentity.contacts.target;
      if (contactsList == null) return false;
      
      return _isIdentityInItemList(contactsList.id, contactIdentityId);
    } catch (e) {
      Get.printError(info: 'Error checking if in contacts: $e');
      return false;
    }
  }

  @override
  Future<bool> isBlocked(String myIdentityId, String blockedIdentityId) async {
    try {
      final myIdentity = _getMyIdentityByIdentityId(myIdentityId);
      if (myIdentity == null) return false;
      
      final blockedList = myIdentity.blockedContacts.target;
      if (blockedList == null) return false;
      
      return _isIdentityInItemList(blockedList.id, blockedIdentityId);
    } catch (e) {
      Get.printError(info: 'Error checking if blocked: $e');
      return false;
    }
  }

  @override
  Future<List<Identity>> searchIdentities(String query) async {
    try {
      final searchQuery = _identityBox
          .query(Identity_.name.contains(query, caseSensitive: false)
              .or(Identity_.email.contains(query, caseSensitive: false))
              .or(Identity_.phoneNumber.contains(query, caseSensitive: false)))
          .order(Identity_.name)
          .build();
      
      final results = searchQuery.find();
      searchQuery.close();
      return results;
    } catch (e) {
      Get.printError(info: 'Error searching identities: $e');
      return [];
    }
  }

  @override
  Future<List<Identity>> getContactsForMyIdentity(String myIdentityId) async {
    try {
      final myIdentity = _getMyIdentityByIdentityId(myIdentityId);
      if (myIdentity == null) return [];
      
      final contactsList = myIdentity.contacts.target;
      if (contactsList == null) return [];
      
      return _getIdentitiesFromItemList(contactsList.id);
    } catch (e) {
      Get.printError(info: 'Error getting contacts for my identity: $e');
      return [];
    }
  }

  @override
  Future<List<Identity>> getBlockedContactsForMyIdentity(String myIdentityId) async {
    try {
      final myIdentity = _getMyIdentityByIdentityId(myIdentityId);
      if (myIdentity == null) return [];
      
      final blockedList = myIdentity.blockedContacts.target;
      if (blockedList == null) return [];
      
      return _getIdentitiesFromItemList(blockedList.id);
    } catch (e) {
      Get.printError(info: 'Error getting blocked contacts for my identity: $e');
      return [];
    }
  }

  @override
  Future<void> deleteIdentity(String identityId) async {
    try {
      await _store.runInTransactionAsync(TxMode.write, () {
        final query = _identityBox
            .query(Identity_.identityId.equals(identityId))
            .build();
        final identity = query.findFirst();
        query.close();
        
        if (identity != null) {
          // Remove from all contact lists and blocked lists
          _removeIdentityFromAllLists(identityId);
          
          // Delete the identity
          _identityBox.remove(identity.id);
        }
      });
    } catch (e) {
      Get.printError(info: 'Error deleting identity: $e');
      rethrow;
    }
  }

  @override
  Future<void> deleteMyIdentity(String myIdentityId) async {
    try {
      await _store.runInTransactionAsync(TxMode.write, () {
        final myIdentity = _getMyIdentityByIdentityId(myIdentityId);
        if (myIdentity != null) {
          _myIdentityBox.remove(myIdentity.id);
        }
      });
    } catch (e) {
      Get.printError(info: 'Error deleting my identity: $e');
      rethrow;
    }
  }

  // Helper methods
  MyIdentity? _getMyIdentityByIdentityId(String identityId) {
    final identityQuery = _identityBox
        .query(Identity_.identityId.equals(identityId))
        .build();
    final identity = identityQuery.findFirst();
    identityQuery.close();
    
    if (identity == null) return null;
    
    final myIdentityQuery = _myIdentityBox
        .query(MyIdentity_.identity.equals(identity.id))
        .build();
    final myIdentity = myIdentityQuery.findFirst();
    myIdentityQuery.close();
    
    return myIdentity;
  }

  Identity? _getIdentityById(String identityId) {
    final query = _identityBox
        .query(Identity_.identityId.equals(identityId))
        .build();
    final identity = query.findFirst();
    query.close();
    return identity;
  }

  int _getIdentityIdFromMyIdentity(String myIdentityId) {
    final identity = _getIdentityById(myIdentityId);
    return identity?.id ?? 0;
  }

  ItemList _createItemList(String title) {
    final itemList = ItemList(
      listId: _uuid.v4(),
      title: title,
      createdAt: DateTime.now(),
    );
    _itemListBox.put(itemList);
    return itemList;
  }

  void _createDefaultItemLists(MyIdentity myIdentity) {
    myIdentity.contacts.target = _createItemList('Contacts');
    myIdentity.blockedContacts.target = _createItemList('Blocked Contacts');
    myIdentity.addresses.target = _createItemList('Addresses');
    myIdentity.medicalRecords.target = _createItemList('Medical Records');
  }

  void _removeIdentityFromItemList(int itemListId, String identityId) {
    final itemJoinQuery = _itemJoinBox
        .query(ItemJoin_.itemList.equals(itemListId))
        .build();
    final itemJoins = itemJoinQuery.find();
    itemJoinQuery.close();
    
    for (final join in itemJoins) {
      final item = join.item.target;
      final relatedIdentity = item?.relatedIdentity.target;
      
      if (relatedIdentity?.identityId == identityId) {
        _itemJoinBox.remove(join.id);
        if (item != null) {
          _itemBox.remove(item.id);
        }
        break;
        }
    }
  }

  bool _isIdentityInItemList(int itemListId, String identityId) {
    final itemJoinQuery = _itemJoinBox
        .query(ItemJoin_.itemList.equals(itemListId))
        .build();
    final itemJoins = itemJoinQuery.find();
    itemJoinQuery.close();
    
    for (final join in itemJoins) {
      final item = join.item.target;
      final relatedIdentity = item?.relatedIdentity.target;
      
      if (relatedIdentity?.identityId == identityId) {
        return true;
      }
    }
    
    return false;
  }

  List<Identity> _getIdentitiesFromItemList(int itemListId) {
    final itemJoinQuery = _itemJoinBox
        .query(ItemJoin_.itemList.equals(itemListId))
        .build();
    final itemJoins = itemJoinQuery.find();
    itemJoinQuery.close();
    
    final identities = <Identity>[];
    
    for (final join in itemJoins) {
      final item = join.item.target;
      final relatedIdentity = item?.relatedIdentity.target;
      
      if (relatedIdentity != null) {
        identities.add(relatedIdentity);
      }
    }
    
    return identities;
  }

  void _removeIdentityFromAllLists(String identityId) {
    final identityQuery = _identityBox
        .query( Identity_.identityId.equals(identityId))
        .build();
    final identity = identityQuery.findFirst();
    identityQuery.close();
    
    if (identity == null) return;
    
    final itemJoinQuery = _itemJoinBox
        .query(ItemJoin_.item.equals(identity.id))
        .build();
    final itemJoins = itemJoinQuery.find();
    itemJoinQuery.close();
    
    for (final join in itemJoins) {
      final itemList = join.itemList.target;
      if (itemList != null) {
        _itemJoinBox.remove(join.id);
      }
    }
  }
}

