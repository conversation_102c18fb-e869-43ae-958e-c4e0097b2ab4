import 'app_services_interface.dart' as app_services;
import 'objectbox_service_interface.dart';
import 'settings_service_interface.dart';
import 'localization_service_interface.dart';

/// Interface for service initialization and dependency management
abstract class IServiceInitializer {
  /// Initialize all application services in the correct order
  Future<void> initializeAllServices();

  /// Initialize a specific service
  Future<T> initializeService<T extends app_services.IInitializableService>();

  /// Get service initialization order
  List<Type> get initializationOrder;

  /// Check if all services are initialized
  bool get allServicesInitialized;

  /// Check if a specific service is initialized
  bool isServiceInitialized<T extends app_services.IInitializableService>();

  /// Get a service instance
  T getService<T extends app_services.IInitializableService>();

  /// Register a service
  void registerService<T extends app_services.IInitializableService>(T service);

  /// Unregister a service
  void unregisterService<T extends app_services.IInitializableService>();

  /// Service lifecycle management
  Future<void> startServices();
  Future<void> stopServices();
  Future<void> restartServices();

  /// Service health checks
  bool checkServiceHealth<T extends app_services.IInitializableService>();
  Map<Type, bool> checkAllServicesHealth();

  /// Service dependencies
  List<Type>
  getServiceDependencies<T extends app_services.IInitializableService>();
  bool areDependenciesSatisfied<T extends app_services.IInitializableService>();

  /// Error handling
  void onServiceInitializationError<
    T extends app_services.IInitializableService
  >(Exception error);
  List<Exception> get initializationErrors;
}

/// Service registry for managing service instances
abstract class IServiceRegistry {
  /// Register a service instance
  void register<T extends app_services.IInitializableService>(T service);

  /// Unregister a service
  void unregister<T extends app_services.IInitializableService>();

  /// Get a service instance
  T get<T extends app_services.IInitializableService>();

  /// Check if a service is registered
  bool isRegistered<T extends app_services.IInitializableService>();

  /// Get all registered services
  List<app_services.IInitializableService> getAllServices();

  /// Clear all services
  void clear();
}

/// Service factory for creating service instances
abstract class IServiceFactory {
  /// Create ObjectBox service
  IObjectboxService createObjectboxService();

  /// Create Settings service
  ISettingsService createSettingsService();

  /// Create Localization service
  ILocalizationService createLocalizationService();

  /// Create service by type
  T createService<T extends app_services.IInitializableService>();
}
