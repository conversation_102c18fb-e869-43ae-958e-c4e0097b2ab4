abstract class SettingsRepository {
  Future<AppSettings> getSettings();
  Future<bool> updateSettings(AppSettings settings);
  Stream<AppSettings> get settingsStream;
  Future<bool> updateTheme(ThemeMode theme);
  Future<bool> updateLanguage(String languageCode);
  Future<bool> updateNotificationSettings(NotificationSettings settings);
  Future<bool> updatePrivacySettings(PrivacySettings settings);
  Future<bool> updateChatSettings(ChatSettings settings);
  Future<bool> resetToDefaults();
  Future<bool> exportSettings();
  Future<bool> importSettings(String settingsData);
}
