# Service Architecture

This directory contains the service layer architecture for the <PERSON>wan application, implementing a clean separation between interfaces and implementations.

## Architecture Overview

```
lib/app/services/
├── services_interface/     # Interface definitions
│   ├── app_services_interface.dart
│   ├── objectbox_service_interface.dart
│   ├── settings_service_interface.dart
│   ├── localization_service_interface.dart
│   ├── service_initializer_interface.dart
│   └── index.dart
├── services_impl/         # Concrete implementations
│   ├── appservices.dart
│   ├── service_initializer_impl.dart
│   └── index.dart
└── README.md
```

## Key Principles

### 1. Interface Segregation
- Each service has a dedicated interface defining its contract
- Interfaces are separated from implementations
- Promotes testability and maintainability

### 2. Dependency Injection
- Services are registered with GetX dependency injection
- Proper initialization order is maintained
- Services can be easily mocked for testing

### 3. Service Lifecycle Management
- Services implement `IInitializableService` for consistent lifecycle
- Proper initialization, startup, and cleanup procedures
- Health checking and error handling

## Service Types

### Core Services

#### 1. ObjectboxService
- **Interface**: `IObjectboxService`
- **Purpose**: Database operations and persistence
- **Dependencies**: None (base service)
- **Features**:
  - Database initialization and management
  - Box access for different entity types
  - Backup and restore capabilities
  - Database statistics and health monitoring

#### 2. LocalizationService
- **Interface**: `ILocalizationService`
- **Purpose**: Internationalization and localization
- **Dependencies**: ObjectboxService (for settings persistence)
- **Features**:
  - Multi-language support (English/Arabic)
  - RTL/LTR text direction handling
  - Locale persistence and management
  - Translation utilities and formatting

#### 3. SettingsService
- **Interface**: `ISettingsService`
- **Purpose**: Application settings management
- **Dependencies**: ObjectboxService (for persistence)
- **Features**:
  - Theme and appearance settings
  - Notification preferences
  - Privacy and security settings
  - Settings validation and export/import

## Usage

### Basic Service Initialization

```dart
import 'package:deewan/app/services/services_impl/index.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize all services
  await initializeAppServices();
  
  runApp(MyApp());
}
```

### Using Services in Your Code

```dart
import 'package:get/get.dart';
import 'package:deewan/app/services/services_interface/index.dart';

class MyController extends GetxController {
  // Get service instances
  final localizationService = Get.find<ILocalizationService>();
  final settingsService = Get.find<ISettingsService>();
  
  void changeLanguage() {
    localizationService.toggleLanguage();
  }
  
  void updateTheme(bool isDark) {
    settingsService.setDarkMode(isDark);
  }
}
```

### Service Health Monitoring

```dart
import 'package:deewan/app/services/services_impl/index.dart';

void checkServiceHealth() {
  final initializer = ServiceInitializer();
  final healthStatus = initializer.checkAllServicesHealth();
  
  healthStatus.forEach((serviceType, isHealthy) {
    print('$serviceType: ${isHealthy ? 'Healthy' : 'Unhealthy'}');
  });
}
```

## Testing

### Mocking Services

```dart
import 'package:mockito/mockito.dart';
import 'package:deewan/app/services/services_interface/index.dart';

class MockLocalizationService extends Mock implements ILocalizationService {}
class MockSettingsService extends Mock implements ISettingsService {}

void main() {
  group('Service Tests', () {
    late MockLocalizationService mockLocalizationService;
    
    setUp(() {
      mockLocalizationService = MockLocalizationService();
      Get.put<ILocalizationService>(mockLocalizationService);
    });
    
    test('should change language', () {
      // Test implementation
    });
  });
}
```

## Service Initialization Order

Services are initialized in dependency order:

1. **ObjectboxService** - Database layer (no dependencies)
2. **LocalizationService** - Depends on ObjectboxService for persistence
3. **SettingsService** - Depends on ObjectboxService for persistence

## Error Handling

- Services implement proper error handling and recovery
- Initialization errors are logged and can be retrieved
- Health checks help identify service issues
- Graceful degradation when services are unavailable

## Extension Points

### Adding New Services

1. Create interface in `services_interface/`
2. Implement interface in `services_impl/`
3. Add to service initializer
4. Update dependency order if needed
5. Export from index files

### Custom Service Factory

```dart
class CustomServiceFactory implements IServiceFactory {
  @override
  T createService<T extends IInitializableService>() {
    // Custom service creation logic
  }
}
```

## Best Practices

1. **Always use interfaces** when injecting services
2. **Initialize services early** in app lifecycle
3. **Handle service failures gracefully**
4. **Mock services in tests** for isolation
5. **Monitor service health** in production
6. **Follow dependency order** when adding new services

## Migration Guide

If you're migrating from the old service architecture:

1. Replace direct service imports with interface imports
2. Use `initializeAppServices()` instead of `initServices()`
3. Update service registration to use the new initializer
4. Update tests to use interface mocks

This architecture provides a solid foundation for scalable, testable, and maintainable service management in the Deewan application.
