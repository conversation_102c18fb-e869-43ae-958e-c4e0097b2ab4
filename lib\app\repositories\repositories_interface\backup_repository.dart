abstract class BackupRepository {
  Future<String?> createBackup();
  Future<bool> restoreBackup(String backupPath);
  Future<List<BackupInfo>> getAvailableBackups();
  Future<bool> deleteBackup(String backupId);
  Future<bool> uploadBackupToCloud(String backupPath);
  Future<String?> downloadBackupFromCloud(String backupId);
  Future<bool> scheduleAutoBackup(BackupSchedule schedule);
  Future<BackupSchedule?> getBackupSchedule();
  Stream<BackupProgress> get backupProgressStream;
  Future<bool> verifyBackupIntegrity(String backupPath);
}
