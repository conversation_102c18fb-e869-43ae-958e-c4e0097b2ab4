abstract class AnalyticsRepository {
  Future<void> initialize();
  Future<void> logEvent(String eventName, Map<String, dynamic> parameters);
  Future<void> setUserId(String userId);
  Future<void> setUserProperties(Map<String, dynamic> properties);
  Future<void> logScreenView(String screenName);
  Future<void> logError(String error, String stackTrace);
  Future<void> logPerformanceMetric(String metricName, double value);
  Future<void> enableAnalytics(bool enabled);
  Future<bool> isAnalyticsEnabled();
  Future<void> resetAnalyticsData();
}
