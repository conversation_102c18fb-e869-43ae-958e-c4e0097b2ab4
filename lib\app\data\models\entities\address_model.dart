import 'package:deewan/app/data/models/entities/item_models.dart' show Item;
import 'package:deewan/app/data/models/entities/media_model.dart';
import 'package:objectbox/objectbox.dart';

@Entity()
class Address {
  @Id()
  final int id = 0;
  @Backlink('address')
  final ToOne<Item>? item = ToOne<Item>();
  final String? building;
  final String? floor;
  final String? appartment; // flat, room, appartment numbers, etc.
  final String? street;
  final String? city;
  final String? state;
  final String? zip;
  final String? country;
  final String? addressId;
  final String? addressLatitude;
  final String? addressLongitude;
  final String? addressCreatedAt;
  final String? addressUpdatedAt;
  final String? addressLabel;
  final String? addressStatus; // active, inactive, deleted ,
  @Backlink('imageUrl')
  final ToMany<AdressImg>? imgAdress = ToMany<AdressImg>();
  final String? addressType; // home, work, school, etc.
  Address(
    this.building,
    this.floor,
    this.appartment,
    this.addressType,
    this.addressId,
    this.addressLabel,
    this.addressLatitude,
    this.addressLongitude,
    this.addressCreatedAt,
    this.addressUpdatedAt,
    this.addressStatus, {
    this.street,
    this.city,
    this.state,
    this.zip,
    this.country,
  });
}
