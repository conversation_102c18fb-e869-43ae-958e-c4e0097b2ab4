// App configuration enums
enum AppLanguage { english, arabic, french, spanish }

enum AppMode { dev, prod }

enum AppOrientation { portrait, landscape }

enum AppThemeMode { light, dark, system }

// Status and state enums
enum StatusRequest {
  none,
  loading,
  success,
  failure,
  serverFailure,
  offlineFailure,
}

enum ConnectionStatus {
  connected,
  disconnected,
  connecting,
  reconnecting,
  failed,
}

enum AuthStatus {
  unknown,
  authenticated,
  unauthenticated,
  loading,
  error,
  expired,
}

enum PresenceStatus { online, offline, away, busy, invisible }

enum NetworkStatus { online, offline, limited, unknown }

enum NetworkQuality { excellent, good, fair, poor }

// File and media enums
enum FileType { image, video, audio, document, pdf, any, other }

enum ImageFormat { jpeg, png, webp, gif, bmp }

enum AudioFormat { mp3, aac, wav, flac, ogg }

enum VideoFormat { mp4, avi, mov, wmv, flv, webm }

// Sync and processing enums
enum SyncStatus { idle, syncing, completed, failed, paused }

enum QueueStatus { idle, processing, paused, error }

enum UploadStatus { idle, uploading, success, failed, cancelled }

enum DownloadStatus { idle, downloading, success, failed, cancelled }

// Permission enums
enum Permission {
  camera,
  microphone,
  storage,
  location,
  contacts,
  notifications,
}

enum PermissionResult { granted, denied, restricted, permanentlyDenied }

enum PermissionStatus { granted, denied, restricted, permanentlyDenied }

// Search and content enums
enum SearchType { message, room, contact, media, all }

enum SortOrder { ascending, descending }

enum SortBy { name, date, popularity, rating, price, size }

enum FilterType { category, price, rating, date, location, type }

// Media playback enums
enum RecordingState { idle, recording, paused, stopped }

enum AudioPlaybackState { idle, playing, paused, stopped, loading, error }

// User and profile enums
enum UserRole { admin, user, moderator, guest }

enum Gender { male, female, other, preferNotToSay }

enum MaritalStatus { single, married, divorced, widowed, other }

enum EducationLevel {
  none,
  primary,
  secondary,
  bachelor,
  master,
  doctorate,
  other,
}

enum EmploymentStatus {
  employed,
  unemployed,
  selfEmployed,
  student,
  retired,
  other,
}

// Notification enums
enum NotificationType { info, success, warning, error }

enum PriorityLevel { low, medium, high, urgent }

// Task and workflow enums
enum TaskStatus { pending, inProgress, completed, cancelled, failed }

enum PaymentStatus {
  pending,
  processing,
  completed,
  failed,
  cancelled,
  refunded,
}

enum OrderStatus {
  pending,
  confirmed,
  processing,
  shipped,
  delivered,
  cancelled,
  returned,
}

// Backup and sync enums
enum BackupFrequency { daily, weekly, monthly, never }

enum ContactSyncStatus { idle, syncing, completed, failed }

// Legacy compatibility
enum ThemeMode { light, dark, system }
