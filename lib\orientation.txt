homepage is divided into two primery column : 1_ contacts 2_ ongoing deal(or finished ,recommended ,  etc  ..)
secondary columns are customized based on user experience ؟؟؟؟؟؟


file to create: homepage(column scrollview of 2 parts )الصفحة الرئيسية \قائمة الجهات والصفقات , 
                drawer , settings  , file manager , about page ,
                contacts page interfaceصفحة جهات الاتصال  (personal/bissiness) ,
                مدير الصفقات deal manager interface/ deal_pageview ,
                حسب نوع الملف -نموذج الادخال  input sheet page/select data (chip models(AI based) /elements on map(loc/contacts) 
                + listview of contacts,terms ,choices ,date etc.. ) 
                file page  حسب نوع الملف لكل صيغة طريقة عرض محددة =العناوين \جهات اتصال\جدول مواعيد \بنود واتفاقيات او عقود ذكية تراكمية الاعتماد

ديوان هو مضيف عام للنمائج او الطلبات او شهادة المصادفة=>الصفقة
تعبئة طلبات : فيزا \وثائق حكومية الخ الخ 
بحيث ان الاطراف المعنية (الحكومة \المؤسسة) تقوم باضافة شروط وقواعد تعبئة النموذج و ربطها مع السيرفر الخاص بها 
 ومن ثم يقوم ديوان باستضافتها فقط واتحصيل العمولة؟؟؟ على كل طلب بشكل منفرد باعتباره طرف في الصفقة لا اكثر
 اي ان : سيرفر الحكومة وسيرفر ديوان وسيفر المستخدم(الهاتف\ملفات ديوان )هي منفصلة وتجتمع في صفقة واحدة لانتاج ملف جديد
 بالتالي سمارت كونتراكت

 


sx elements  sx symptom model & 
             syndrom,ddx,dx,known case,age screening  …to be classified in django
             dx critiria model for each syndrom = consist of list of sx elements + questinnare 
             macro case is  a hshed dx which is more important than litreture's dx ,
             ie each person has his own case,ie individualized dx or case
             هذه عناصر مركبة تشمل كل عناصر ديوان 
             و تقارن الطلبات المستقبلة من المستخدم بنماذج في ديجانجو 
             لكل عنصر وتكون شاملة في كل احتمالات عناصرها  
             ترسل للمستخدم اذا كانت عناصر طلبه موجودة في العنصر المطلوب
             وبالتحديد يرسل عناصر المكونة لهذا العنصر المركب (التشخيص الخ الخ)
             لهذا هذه العناصر المركبة مهمة في ايجاد باقي عناصر ديوان

lab/ سلعة
med/سلعة  مصادقة
referral / مستند
procedure /سلعة
plan-protocol / مستند مصادقة+سلعة
notes / مستند حُر
traige/ مستند sx 
// اختيار الملف المناسب للطلب المناسب -الاعراض -في حالة ان الطلب هو عبارة عن استشارة 
وكل استشاره لها متطلبات لملف الاعراض او ملف الصحي كامل
اسنشاره اخصاب او زراعة شعر =>ملف الصحي و نموذج اسئلة خاصة -موحد-
 من ديوان وايضا خاص من المركز المعني, يملاء  قبل التسجيل 
//انواع الطلبات الاخرى هي تحديد الحالة والمرض المعرف مسبقا 
والبحث عن مركز متخصص في الحالة  مباشرة 


انواع الملفات المكونة للحساب الرئيسي universal deewan files :

 healthcare file :  referral report or admission report-appointments-scheduls /
  labList-product-items , medication-items / questionnaire form /
                     macro case\dx\ddx\mhx-spcial cards=specific data fields for specific purpose
therefore we don't need special files to specific sector ,but universal file to all sectors

address 
calindar مواعيد
wallet

اسم الكتاب:
messurable user rights حقوق المستخدم القابلة للقياس

Bind.isRegistered<YouController>()

 صفقة التكسي : يقوم البوت-السيرفر- بنشر موقع السيارة خلال الرحلة و تسجيل صوت \صورة خلال الرحلة في فوفة الصفقة للمحادة 
 وهو نفسه بوت تنظيمي رقابي يعمل تلقائيا من قبل السيرفر لتنظيم الصفقات و مراقبة جودتها في جميع انواع الصفقات
 وهو ايضا جهة اتصال -صفحة بزنز-ليس للتحدث معه
 لان بهذا سيكون مع علاقة مع كل المستخدمين وهذا مرفوض حيث وجود جميع المستخدمين باتصال مع حساب واحد يسهل الاختراق 
 وبالتالي الحساب فقط لنشر التحديثات و عرض الصفقات التي يدعمها
 وعليه : يمكن انشاء بوت مماثل للصفقة الصحية و التسوق و الامور القانونية




//ملفات ديوان :
//ملف ادارة  حساب ديوان
//الملف الطبي
// الكوكيز=> بيانات التصفح
// الرصيد البنكي المالي
//ملف رصيد الانترنت ,
