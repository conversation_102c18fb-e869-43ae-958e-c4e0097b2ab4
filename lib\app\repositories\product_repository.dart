import 'dart:async';
import 'package:deewan/app/data/models/entities/item_models.dart';

abstract class ProductRepositoryInterface {
  Stream<List<Product>> watchProducts({String? storeId, String? category});
  Stream<Product?> watchProduct(String productId);
  Stream<List<Product>> watchFeaturedProducts({String? storeId});
  Stream<List<Product>> watchProductsByStore(String storeId);
  
  Future<List<Product>> getProducts({String? storeId, String? category, 
      int limit = 20, int offset = 0});
  Future<Product?> getProductById(String productId);
  Future<List<Product>> getProductsByStore(String storeId, {int limit = 20, int offset = 0});
  Future<List<Product>> getFeaturedProducts({String? storeId, int limit = 10});
  Future<List<Product>> getProductsByCategory(String category, {int limit = 20, int offset = 0});
  
  Future<void> saveProduct(Product product);
  Future<void> updateProduct(Product product);
  Future<void> deleteProduct(String productId);
  Future<void> updateProductStock(String productId, int stockQuantity);
  Future<void> updateProductPrice(String productId, double price);
  Future<void> toggleProductAvailability(String productId, bool isAvailable);
  
  Future<List<Product>> searchProducts(String query, {String? storeId, String? category,
      double? minPrice, double? maxPrice});
  Future<List<String>> getProductCategories({String? storeId});
  Future<void> addProductToWishlist(String productId, String userProfileId);
  Future<void> removeProductFromWishlist(String productId, String userProfileId);
  Future<List<Product>> getWishlistProducts(String userProfileId);
}