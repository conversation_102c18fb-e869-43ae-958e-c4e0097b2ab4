/// Storage keys for secure storage and shared preferences
class StorageKeys {
  StorageKeys._();

  // Authentication Keys
  static const String accessToken = 'access_token';
  static const String refreshToken = 'refresh_token';
  static const String userId = 'user_id';
  static const String userEmail = 'user_email';
  static const String userName = 'user_name';
  static const String userProfile = 'user_profile';
  static const String isLoggedIn = 'is_logged_in';
  static const String loginTimestamp = 'login_timestamp';
  static const String lastLoginDate = 'last_login_date';
  static const String biometricEnabled = 'biometric_enabled';
  static const String rememberMe = 'remember_me';

  // App Settings Keys
  static const String themeMode = 'theme_mode';
  static const String language = 'language';
  static const String locale = 'locale';
  static const String fontSize = 'font_size';
  static const String notificationsEnabled = 'notifications_enabled';
  static const String soundEnabled = 'sound_enabled';
  static const String vibrationEnabled = 'vibration_enabled';
  static const String autoBackup = 'auto_backup';
  static const String dataUsageOptimization = 'data_usage_optimization';

  // Onboarding Keys
  static const String isFirstLaunch = 'is_first_launch';
  static const String onboardingCompleted = 'onboarding_completed';
  static const String tutorialShown = 'tutorial_shown';
  static const String welcomeScreenSeen = 'welcome_screen_seen';

  // Cache Keys
  static const String cacheVersion = 'cache_version';
  static const String lastCacheUpdate = 'last_cache_update';
  static const String offlineData = 'offline_data';
  static const String cachedUserData = 'cached_user_data';
  static const String cachedSettings = 'cached_settings';

  // Security Keys
  static const String encryptionKey = 'encryption_key';
  static const String deviceId = 'device_id';
  static const String sessionId = 'session_id';
  static const String lastSecurityCheck = 'last_security_check';
  static const String failedLoginAttempts = 'failed_login_attempts';
  static const String accountLockTime = 'account_lock_time';

  // Feature Flags
  static const String featureFlags = 'feature_flags';
  static const String betaFeatures = 'beta_features';
  static const String experimentalFeatures = 'experimental_features';

  // Analytics Keys
  static const String analyticsEnabled = 'analytics_enabled';
  static const String crashReportingEnabled = 'crash_reporting_enabled';
  static const String performanceMonitoring = 'performance_monitoring';
  static const String userIdAnalytics = 'user_id_analytics';

  // Sync Keys
  static const String lastSyncTime = 'last_sync_time';
  static const String syncEnabled = 'sync_enabled';
  static const String pendingSyncData = 'pending_sync_data';
  static const String syncConflicts = 'sync_conflicts';

  // Backup Keys
  static const String lastBackupTime = 'last_backup_time';
  static const String backupEnabled = 'backup_enabled';
  static const String backupLocation = 'backup_location';
  static const String autoBackupFrequency = 'auto_backup_frequency';

  // Debug Keys
  static const String debugMode = 'debug_mode';
  static const String logLevel = 'log_level';
  static const String debugLogs = 'debug_logs';

  // Temporary Keys
  static const String tempData = 'temp_data';
  static const String tempFiles = 'temp_files';
  static const String tempCache = 'temp_cache';

  // App State Keys
  static const String appVersion = 'app_version';
  static const String buildNumber = 'build_number';
  static const String lastUpdateCheck = 'last_update_check';
  static const String updateAvailable = 'update_available';
  static const String maintenanceMode = 'maintenance_mode';

  // Notification Keys
  static const String notificationTokens = 'notification_tokens';
  static const String notificationSettings = 'notification_settings';
  static const String lastNotificationTime = 'last_notification_time';
  static const String notificationHistory = 'notification_history';

  // Location Keys
  static const String lastKnownLocation = 'last_known_location';
  static const String locationPermissionGranted = 'location_permission_granted';
  static const String locationTrackingEnabled = 'location_tracking_enabled';

  // Media Keys
  static const String downloadedMedia = 'downloaded_media';
  static const String mediaCache = 'media_cache';
  static const String mediaQuality = 'media_quality';
  static const String autoDownload = 'auto_download';

  // Search Keys
  static const String searchHistory = 'search_history';
  static const String recentSearches = 'recent_searches';
  static const String searchSuggestions = 'search_suggestions';

  // Favorites Keys
  static const String favorites = 'favorites';
  static const String bookmarks = 'bookmarks';
  static const String recentItems = 'recent_items';
  static const String watchlist = 'watchlist';
}
