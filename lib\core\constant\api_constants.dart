/// API-specific constants for the application
class ApiConstants {
  ApiConstants._();

  // Base URLs
  static const String baseUrl = 'https://api.yourapp.com/v1';
  static const String baseUrlDev = 'https://dev-api.yourapp.com/v1';
  static const String baseUrlStaging = 'https://staging-api.yourapp.com/v1';

  // API Endpoints
  static const String auth = '/auth';
  static const String login = '$auth/login';
  static const String register = '$auth/register';
  static const String logout = '$auth/logout';
  static const String refreshToken = '$auth/refresh';
  static const String forgotPassword = '$auth/forgot-password';
  static const String resetPassword = '$auth/reset-password';
  static const String verifyEmail = '$auth/verify-email';
  static const String resendVerification = '$auth/resend-verification';

  // User endpoints
  static const String users = '/users';
  static const String profile = '$users/profile';
  static const String updateProfile = '$users/profile';
  static const String changePassword = '$users/change-password';
  static const String deleteAccount = '$users/delete';

  // Content endpoints
  static const String posts = '/posts';
  static const String comments = '/comments';
  static const String likes = '/likes';
  static const String shares = '/shares';

  // File upload endpoints
  static const String upload = '/upload';
  static const String uploadImage = '$upload/image';
  static const String uploadVideo = '$upload/video';
  static const String uploadDocument = '$upload/document';

  // Notification endpoints
  static const String notifications = '/notifications';
  static const String markAsRead = '$notifications/read';
  static const String markAllAsRead = '$notifications/read-all';

  // Settings endpoints
  static const String settings = '/settings';
  static const String preferences = '$settings/preferences';

  // API Headers
  static const String contentType = 'Content-Type';
  static const String authorization = 'Authorization';
  static const String accept = 'Accept';
  static const String userAgent = 'User-Agent';
  static const String xApiKey = 'X-API-Key';

  // Content Types
  static const String applicationJson = 'application/json';
  static const String multipartFormData = 'multipart/form-data';
  static const String applicationFormUrlEncoded =
      'application/x-www-form-urlencoded';

  // API Response Keys
  static const String data = 'data';
  static const String message = 'message';
  static const String status = 'status';
  static const String error = 'error';
  static const String errors = 'errors';
  static const String meta = 'meta';
  static const String pagination = 'pagination';

  // Pagination Keys
  static const String page = 'page';
  static const String perPage = 'per_page';
  static const String total = 'total';
  static const String totalPages = 'total_pages';
  static const String hasMore = 'has_more';

  // Query Parameters
  static const String search = 'search';
  static const String filter = 'filter';
  static const String sort = 'sort';
  static const String order = 'order';
  static const String limit = 'limit';
  static const String offset = 'offset';

  // Sort Orders
  static const String ascending = 'asc';
  static const String descending = 'desc';

  // API Versions
  static const String v1 = 'v1';
  static const String v2 = 'v2';

  // Rate Limiting
  static const String rateLimitRemaining = 'X-RateLimit-Remaining';
  static const String rateLimitReset = 'X-RateLimit-Reset';
  static const String rateLimitLimit = 'X-RateLimit-Limit';
}
