import 'package:deewan/app/data/models/entities/address_model.dart';
import 'package:deewan/app/data/models/entities/identity_model.dart';
import 'package:deewan/app/data/models/entities/message_models.dart';
import 'package:deewan/app/data/models/entities/item_models.dart';
import 'package:objectbox/objectbox.dart';

//////////////////message types ///////////////////////
@Entity()
class ImageUrl {
  @Id()
  final int id = 0;
  final String? imageUrl;
  final String? imageType;
  final DateTime? createdAt;
  final ToMany<ItemImage>? itemImage = ToMany<ItemImage>();
  final ToMany<MsgImage>? msgImage = ToMany<MsgImage>();
  final ToMany<MsgAdress>? msgAdress = ToMany<MsgAdress>();

  ImageUrl(
    this.imageUrl,
    this.imageType,
    this.createdAt,
  );
}

@Entity()
class IdentityImg {
  @Id()
  final int id = 0;
  final ToOne<ImageUrl>? imageUrl = ToOne<ImageUrl>();
  final ToOne<Identity>? identity = ToOne<Identity>();
  IdentityImg();
}

@Entity()
class ItemImage {
  @Id()
  final int id = 0;
  final ToOne<Item> item = ToOne<Item>();
  final ToOne<ImageUrl> imageUrl = ToOne<ImageUrl>();
  ItemImage();
}

@Entity()
class MsgImage {
  @Id()
  final int id = 0;
  final ToOne<Message>? message = ToOne<Message>();
  final ToOne<ImageUrl>? imageUrl = ToOne<ImageUrl>();
  MsgImage();
}

@Entity()
class AdressImg {
  @Id()
  final int id = 0;
  final ToOne<ImageUrl>? imageUrl = ToOne<ImageUrl>();
  final ToOne<Address>? address = ToOne<Address>();
  AdressImg();
}

@Entity()
class VideoUrl {
  @Id()
  final int id = 0;
  final String? videoUrl;
  final String? videoType;
  final DateTime? createdAt;
  VideoUrl(
    this.videoUrl,
    this.videoType,
    this.createdAt,
  );
}

@Entity()
class AudioUrl {
  @Id()
  final int id = 0;
  final String? audioUrl;
  final String? audioType;
  final DateTime? createdAt;
  AudioUrl(
    this.audioUrl,
    this.audioType,
    this.createdAt,
  );
}

@Entity()
class FileUrl {
  @Id()
  final int id = 0;
  final String? fileUrl;
  final String? fileType;
  final DateTime? createdAt;
  FileUrl(
    this.fileUrl,
    this.fileType,
    this.createdAt,
  );
}

@Entity()
class LinkPreview {
  @Id()
  final int id = 0;
  final String? linkUrl;
  final String? linkTitle;
  final String? linkDescription;
  final String? linkImage;
  LinkPreview(
    this.linkUrl,
    this.linkTitle,
    this.linkDescription,
    this.linkImage,
  );
}
//   @Entity()
// class ItemMessage {
//   @Id()
//   final String? id;
//   @Backlink('id')
//   final  item= ToOne<Item>();
//   get imageUrl => item.target!.imageUrl;
//   ItemMessage(
//     this.id,
//   );
// }
