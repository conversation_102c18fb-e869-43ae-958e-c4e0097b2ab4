  import 'package:deewan/app/data/models/entities/identity_model.dart';
import 'package:deewan/app/data/models/entities/item_models.dart';
import 'package:objectbox/objectbox.dart';


@Entity()
class ContactPage {// aka contact card
  @Id()
  final String? id;
  final ToOne<Identity> identity = ToOne<Identity>();// this page is has information about this identity
  final String? name;
  final String? workingHours;
  final String? description;
  final bool? isBussiness;
   @Backlink('contactPage')//??????????????????????????
  final ToMany<ItemList>? store = ToMany<ItemList>();// this could be a shopping list or list of contacts or list of addresses or followers
  // @Backlink('contactPage')
  // final ToMany<ItemList>? info = ToMany<ItemList>();

     ContactPage (this.name, this.id,
      this.description, this.isBussiness, this.workingHours);
}