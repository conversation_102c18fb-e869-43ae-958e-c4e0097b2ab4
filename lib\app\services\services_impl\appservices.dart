import 'package:get/get.dart';
import '../../../core/utils/classes/objectbox.dart';
import '../../../core/localization/localization_service.dart';
import '../services_interface/app_services_interface.dart';
import '../services_interface/objectbox_service_interface.dart'
    as objectbox_interface;
import '../services_interface/settings_service_interface.dart'
    as settings_interface;
import '../services_interface/service_initializer_interface.dart';

abstract class InitializableService extends GetxService
    implements IInitializableService {
  bool _initialized = false;

  /// Abstract method for specific initialization
  Future<void> _init();

  @override
  Future<void> initialize() async {
    await _init();
    _initialized = true;
  }

  @override
  Future<dynamic> ensureInitialized() async {
    if (!_initialized) {
      await initialize();
    }
    return this;
  }

  @override
  bool get isInitialized => _initialized;

  @override
  bool get isReady => _initialized;
}

class ObjectboxService extends InitializableService
    implements objectbox_interface.IObjectboxService {
  @override
  late ObjectBox objectbox;

  @override
  Future<ObjectboxService> _init() async {
    objectbox = await ObjectBox.create();
    return this;
  }

  @override
  ObjectBox get instance => objectbox;

  @override
  dynamic getBox<T>() {
    // Implementation depends on your ObjectBox setup
    // Return the appropriate box for type T
    return null; // Placeholder
  }

  @override
  Future<void> close() async {
    // Close ObjectBox instance
    // Implementation depends on your ObjectBox setup
  }

  @override
  dynamic get appBox => null; // Placeholder - implement based on your ObjectBox setup

  @override
  dynamic get settingsBox => null; // Placeholder - implement based on your ObjectBox setup

  @override
  Future<void> initializeDatabase() async {
    await _init();
  }

  @override
  Future<void> closeDatabase() async {
    await close();
  }

  @override
  bool get isDatabaseReady => isReady;

  @override
  Future<bool> backupDatabase(String path) async {
    // Implement backup logic
    return false; // Placeholder
  }

  @override
  Future<bool> restoreDatabase(String path) async {
    // Implement restore logic
    return false; // Placeholder
  }

  @override
  Map<String, dynamic> getDatabaseStats() {
    // Return database statistics
    return {}; // Placeholder
  }
}

class SettingsService extends InitializableService {
  @override
  Future<void> _init() async {}
}

Future<void> initServices() async {
  print('Starting services...');

  // Initialize ObjectBox service first (required for persistence)
  final objectboxService = ObjectboxService();
  await objectboxService.ensureInitialized();
  Get.put(objectboxService, permanent: true);

  // Initialize LocalizationService (depends on ObjectBox for settings persistence)
  Get.put(LocalizationService(), permanent: true);

  // Initialize other services
  final settingsService = SettingsService();
  await settingsService.ensureInitialized();
  Get.put(settingsService, permanent: true);

  print('All services started successfully.');
}

/// Service initialization complete.
///
/// This file provides:
/// - InitializableService base class for async service initialization
/// - ObjectboxService for database operations
/// - SettingsService for app settings management
/// - LocalizationService integration for language preferences
///
/// Services are initialized in the correct dependency order:
/// 1. ObjectboxService (database layer)
/// 2. LocalizationService (depends on ObjectBox for persistence)
/// 3. SettingsService (general app settings)
