// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'package:deewan/app/data/models/entities/address_model.dart';
import 'package:deewan/app/data/models/entities/identity_model.dart';
import 'package:deewan/app/data/models/entities/item_models.dart';

import 'package:objectbox/objectbox.dart';

/// Abstract class representing a message in the chat.
/// Subclasses should implement specific message content and rendering logic.
@Entity()
class Message {
  @Id()
  int id = 0;
  final int messageId;
  final ToOne<Room>? chatRoom = ToOne<Room>();
  final String
      senderId; //private account id or business account id or contact id
  final DateTime creationtimestamp;
  final bool? isSent;
  final bool? isEdited;
  final DateTime? editedTimestamp;
  final String? starType;
  final bool? isSeen;
  final DateTime? deliveredTimestamp;
  final DateTime? sentTimestamp;
  final DateTime? seenTimestamp;
  final String? messageContent;
  @Index() // Index for efficient chronological queries
  final DateTime? creationTimestamp;
  bool? isFromMe(String currentUserId) {
    return senderId == currentUserId;
  }

  bool isModified() => isEdited ?? false;
  DateTime getLastUpdateTime() => editedTimestamp ?? creationtimestamp;

  Message(
    this.starType,
    this.isSent,
    this.isEdited,
    this.editedTimestamp,
    this.isSeen,
    this.deliveredTimestamp,
    this.sentTimestamp,
    this.seenTimestamp,
    this.messageContent,
    this.creationTimestamp, {
    required this.senderId,
    required this.messageId,
    required this.creationtimestamp,
  });
}

@Entity()
class Room {
  @Id()
  int id = 0;
  final String roomId = '';
  final String roomTitle = '';
  @Backlink('chatRoom')
  final ToMany<Message>? messages = ToMany<Message>();
  final List<Message>? pinnedMessages;
  @Backlink('room')
  final ToMany<RoomParticipant> participants = ToMany<RoomParticipant>();
  final ToOne<PrivateRoom>? privateRoom = ToOne<PrivateRoom>();
  final ToOne<PublicChannalRoom>? channelRoom = ToOne<PublicChannalRoom>();
  final ToOne<OrderRoom>? orderRoom = ToOne<OrderRoom>  ();

  final String? status; // if deal : ongoing, completed, cancelled, deleted,
  final bool? isArchived;
  final bool? isMuted;
  final bool? isPinned;
  final bool? isStarred;
  final bool? isHidden;
  final bool? isBlocked;
  final bool? isRead;
  final DateTime? lastMessageTimestamp;
  final String? lastMessageContent;
  final String? lastMessageAuther;
  final String? lastMessageDeliveryStatus;
  final String? lastMessageStarType;
  final int? unreadMessagesCount;

  // final bool? isHealthCareRoom;// generally the room are chatty but can be for deal and healthcare and other purposes like group chat
  Room(
    this.status,
    this.isArchived,
    this.isMuted,
    this.isPinned,
    this.isStarred,
    this.isHidden,
    this.isBlocked,
    this.isRead,
    this.lastMessageTimestamp,
    this.lastMessageContent,
    this.lastMessageAuther,
    this.lastMessageDeliveryStatus,
    this.lastMessageStarType,
    this.unreadMessagesCount,
    this.pinnedMessages,
  );
}

class PrivateRoom {
  @Id()
  final int id = 0;
  final ToOne<Room>? room = ToOne<Room>();
  PrivateRoom(
  );
}

@Entity()
class PublicChannalRoom {
  @Id()
  final int id = 0;

  final ToOne<Room>? room = ToOne<Room>();
  //final ContactList? contactList = ToOne<ContactList>();
  PublicChannalRoom();

}

class OrderRoom {
  @Id()
  final int id = 0;
  final ToOne<Room>? room = ToOne<Room>();
  final String? orderTitle;
  final String? orderCategory;
  final DateTime? orderDate; // the date of the order
  final bool? isArchived;
  final bool? isConfirmed;
  final bool? isAccomplished;
  final bool? isCancelled;
  final bool? isWaitForConfirmation;
  final bool? isOnGoing;
  final String?
      orderStatus; // ready ongoing, cancelled, deleted ,accepted ,accomplished
  final Message?
      orderMessage; //main/pinned message for deal, package or product or mao location , or tiket number for appointment , payment ....
  final Identity createdBy;
  OrderRoom(
      this.orderTitle,
      this.orderStatus,
      this.createdBy,
      this.orderMessage,
      this.orderCategory,
      this.orderDate,
      this.isArchived,
      this.isConfirmed,
      this.isCancelled,
      this.isWaitForConfirmation,
      this.isAccomplished,
      this.isOnGoing,
      );
}

//////////////////message types ///////////////////////
@Entity()
class ImageUrl {
  @Id()
  final int id = 0;
  final String? imageUrl;
  final String? imageType;
  final DateTime? createdAt;
  final ToMany<ItemImage>? itemImage = ToMany<ItemImage>();
  final ToMany<MsgImage>? msgImage = ToMany<MsgImage>();
  final ToMany<MsgAdress>? msgAdress = ToMany<MsgAdress>();
  
  ImageUrl(
    this.imageUrl,
    this.imageType,
    this.createdAt,
  );
}
// @Entity()
// class IdentityImg {
//   @Id()
//   final int id = 0;
//   final ToOne<ImageUrl>? imageUrl = ToOne<ImageUrl>();
//   final ToOne<Identity>? identity = ToOne<Identity>();
//   IdentityImg(
//   );
// }
@Entity()
class ItemImage{
  @Id()
  final int id = 0;
  final ToOne<Item> item = ToOne<Item>(); 
  final ToOne<ImageUrl> imageUrl = ToOne<ImageUrl>();
  ItemImage(
  );
}
@Entity()
class MsgImage {
  @Id()
  final int id = 0;
  final ToOne<Message>? message = ToOne<Message>();
  final ToOne<ImageUrl>? imageUrl = ToOne<ImageUrl>();
  MsgImage(
  );
}
@Entity()
class MsgAdress {
  @Id()
  final int id = 0;
  final ToOne<ImageUrl>? imageUrl = ToOne<ImageUrl>();
  final ToOne<Address>? address = ToOne<Address>();
  MsgAdress(
  );
}

@Entity()
class VideoUrl {
  @Id()
  final int id = 0;
  final String? videoUrl;
  final String? videoType;
  final DateTime? createdAt;
  VideoUrl(
    this.videoUrl,
    this.videoType,
    this.createdAt,
  );
}

@Entity()
class AudioUrl {
  @Id()
  final int id = 0;
  final String? audioUrl;
  final String? audioType;
  final DateTime? createdAt;
  AudioUrl(
    this.audioUrl,
    this.audioType,
    this.createdAt,
  );
}

@Entity()
class FileUrl {
  @Id()
  final int id = 0;
  final String? fileUrl;
  final String? fileType;
  final DateTime? createdAt;
  FileUrl(
    this.fileUrl,
    this.fileType,
    this.createdAt,
  );
}

@Entity()
class LinkPreview {
  @Id()
  final int id = 0;
  final String? linkUrl;
  final String? linkTitle;
  final String? linkDescription;
  final String? linkImage;
  LinkPreview(
    this.linkUrl,
    this.linkTitle,
    this.linkDescription,
    this.linkImage,
  );
}
//   @Entity()
// class ItemMessage {
//   @Id()
//   final String? id;
//   @Backlink('id')
//   final  item= ToOne<Item>();
//   get imageUrl => item.target!.imageUrl;
//   ItemMessage(
//     this.id,
//   );
// }

enum StarType {
  like,
  dislike,
  heart,

  smile,
  laugh,
  angry
} //the type of star/reaction given to the message

enum PollType {
  singleChoice,
  multipleChoice,

  rating
} //the type of poll created for the message

enum Messagetype {
  text,
  reply,
  forward,
  image,
  video,
  audio,
  file,
  location,
  sticker,
  reaction,
  poll,
  contacts, //contacts card to show contact details of person/business as a card in chat
  gif,
  link,
  calendar,
  appointment, // time and date and location and target account or location or
  productsCart, // on click it opens a page that contains products to be deliverd /sell/ buy...
  healthCareElement, // healthcare profile elements : lap , med ,prosedure ,ddx reports , xr, AI reports that contains spt sympt....
  deal, //(ongoing to join or an empty template deal(modified to specific use) ) information
  currency,
  package,

  unknown, // Added "unknown" type
  // Added "custom" type
  custom, // Added "custom" type
}

enum MessageDeliveryStatus {
  //to show the status of delivery only on listile of chat which will be the last message in the chat
  delivered,
  sent,
  seen,
  pending,
  failed,
}
