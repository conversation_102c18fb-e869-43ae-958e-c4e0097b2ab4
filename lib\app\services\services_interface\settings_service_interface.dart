import 'package:flutter/material.dart';
import 'app_services_interface.dart';

/// Interface for application settings management
abstract class ISettingsService extends IInitializableService {
  /// Load all settings from persistent storage
  Future<void> loadSettings();
  
  /// Save all settings to persistent storage
  Future<void> saveSettings();
  
  /// Reset all settings to default values
  Future<void> resetToDefaults();
  
  /// Language Settings
  Locale get currentLanguage;
  Future<void> setLanguage(Locale locale);
  List<Locale> get supportedLanguages;
  
  /// Theme Settings
  bool get isDarkMode;
  Future<void> setDarkMode(bool enabled);
  ThemeMode get themeMode;
  Future<void> setThemeMode(ThemeMode mode);
  
  /// Notification Settings
  bool get notificationsEnabled;
  Future<void> setNotificationsEnabled(bool enabled);
  bool get pushNotificationsEnabled;
  Future<void> setPushNotificationsEnabled(bool enabled);
  
  /// Background Work Settings
  bool get backgroundWorkEnabled;
  Future<void> setBackgroundWorkEnabled(bool enabled);
  
  /// Privacy Settings
  bool get analyticsEnabled;
  Future<void> setAnalyticsEnabled(bool enabled);
  bool get crashReportingEnabled;
  Future<void> setCrashReportingEnabled(bool enabled);
  
  /// App Behavior Settings
  bool get autoSaveEnabled;
  Future<void> setAutoSaveEnabled(bool enabled);
  Duration get autoSaveInterval;
  Future<void> setAutoSaveInterval(Duration interval);
  
  /// Generic setting operations
  T? getSetting<T>(String key, {T? defaultValue});
  Future<void> setSetting<T>(String key, T value);
  Future<void> removeSetting(String key);
  bool hasSetting(String key);
  
  /// Bulk operations
  Map<String, dynamic> getAllSettings();
  Future<void> setMultipleSettings(Map<String, dynamic> settings);
  Future<void> clearAllSettings();
  
  /// Settings validation
  bool validateSettings();
  List<String> getInvalidSettings();
  
  /// Settings export/import
  Future<String> exportSettings();
  Future<bool> importSettings(String settingsJson);
}
