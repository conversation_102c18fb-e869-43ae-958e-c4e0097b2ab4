import 'dart:async';

abstract class OfflineQueueRepositoryInterface {
  Stream<List<QueuedItem>> watchQueuedItems();
  Stream<int> watchPendingCount();
  
  Future<List<QueuedItem>> getPendingItems({QueueItemType? type});
  Future<List<QueuedItem>> getFailedItems({int? maxRetries});
  Future<QueuedItem?> getQueuedItemById(String queueId);
  
  Future<void> queueItem(QueuedItem item);
  Future<void> updateItemStatus(String queueId, QueueItemStatus status, 
      {String? errorMessage, Map<String, dynamic>? responseData});
  Future<void> incrementRetryCount(String queueId);
  Future<void> removeFromQueue(String queueId);
  Future<void> clearProcessedItems();
  
  Future<int> getPendingCount({QueueItemType? type});
  Future<void> retryFailedItems();
  Future<void> prioritizeItem(String queueId, int priority);
}
