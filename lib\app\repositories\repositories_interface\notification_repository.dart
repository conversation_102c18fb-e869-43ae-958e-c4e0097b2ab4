abstract class NotificationRepository {
  Future<bool> initialize();
  Future<String?> getDeviceToken();
  Future<bool> requestPermissions();
  Future<bool> scheduleLocalNotification(LocalNotification notification);
  Future<bool> cancelNotification(String notificationId);
  Future<bool> cancelAllNotifications();
  Stream<NotificationAction> get notificationActionStream;
  Future<bool> updateNotificationSettings(NotificationSettings settings);
  Future<NotificationSettings> getNotificationSettings();
  Future<bool> setBadgeCount(int count);
  Future<bool> clearBadge();
}
