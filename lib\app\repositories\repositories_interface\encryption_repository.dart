abstract class EncryptionRepository {
  Future<String> generateKeyPair();
  Future<String> encrypt(String plainText, String publicKey);
  Future<String> decrypt(String cipherText, String privateKey);
  Future<String> hash(String input);
  Future<bool> verifyHash(String input, String hash);
  Future<String> generateSecureToken();
  Future<String> encryptMessage(String message, String roomId);
  Future<String> decryptMessage(String encryptedMessage, String roomId);
  Future<String> generateRoomKey(String roomId);
  Future<bool> storePrivateKey(String key);
  Future<String?> getPrivateKey();
  Future<bool> deletePrivateKey();
}
