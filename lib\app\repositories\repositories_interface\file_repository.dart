abstract class FileRepository {
  Future<String?> uploadFile(String filePath, FileType fileType);
  Future<String?> downloadFile(String url, String fileName);
  Future<bool> deleteFile(String filePath);
  Future<String?> pickFile(FileType fileType);
  Future<String?> capturePhoto();
  Future<String?> recordVideo();
  Future<String?> recordAudio();
  Future<List<String>> pickMultipleFiles(FileType fileType);
  Future<String?> compressImage(String imagePath, int quality);
  Future<String?> compressVideo(String videoPath);
  Future<int> getFileSize(String filePath);
  Future<bool> fileExists(String filePath);
  Future<String> getFileHash(String filePath);
}
