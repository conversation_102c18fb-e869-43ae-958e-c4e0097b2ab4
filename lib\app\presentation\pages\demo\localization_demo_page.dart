import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../controllers/sittings/settingscontroller.dart';
import '../../widgets/language_selector.dart';
import '../../../core/localization/localization_service.dart';

/// Demo page to showcase the localization system features
class LocalizationDemoPage extends StatelessWidget {
  const LocalizationDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SettingsController>();
    final localizationService = Get.find<LocalizationService>();

    return Scaffold(
      appBar: AppBar(
        title: Text('Localization Demo'),
        actions: [
          LanguageToggleButton(),
          const SizedBox(width: 8),
        ],
      ),
      body: Obx(() => SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Current Language Info
                _buildInfoCard(
                  title: 'Current Language Info',
                  children: [
                    _buildInfoRow('Language', controller.currentLanguageName),
                    _buildInfoRow('Locale', controller.currentLocale.toString()),
                    _buildInfoRow('Language Code', localizationService.currentLanguageCode),
                    _buildInfoRow('Country Code', localizationService.currentCountryCode ?? 'N/A'),
                    _buildInfoRow('Text Direction', localizationService.isRTL ? 'RTL' : 'LTR'),
                    _buildInfoRow('Is Arabic', localizationService.isArabic.toString()),
                    _buildInfoRow('Is English', localizationService.isEnglish.toString()),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // Language Selectors Demo
                _buildInfoCard(
                  title: 'Language Selectors',
                  children: [
                    const Text('Full Language Selector:'),
                    const SizedBox(height: 8),
                    const LanguageSelector(),
                    const SizedBox(height: 16),
                    
                    const Text('Compact Language Selector:'),
                    const SizedBox(height: 8),
                    const LanguageSelector(compact: true),
                    const SizedBox(height: 16),
                    
                    const Text('Language Chip:'),
                    const SizedBox(height: 8),
                    const LanguageChip(),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // Translation Examples
                _buildInfoCard(
                  title: 'Translation Examples',
                  children: [
                    _buildTranslationExample('title'),
                    _buildTranslationExample('home'),
                    _buildTranslationExample('settings'),
                    _buildTranslationExample('language'),
                    _buildTranslationExample('login'),
                    _buildTranslationExample('logout'),
                    _buildTranslationExample('email'),
                    _buildTranslationExample('password'),
                    _buildTranslationExample('save'),
                    _buildTranslationExample('cancel'),
                    _buildTranslationExample('confirm'),
                    _buildTranslationExample('welcomeToDeewan'),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // RTL Layout Demo
                _buildInfoCard(
                  title: 'RTL Layout Demo',
                  children: [
                    Container(
                      padding: localizationService.getDirectionalPadding(
                        start: 20,
                        top: 10,
                        end: 40,
                        bottom: 10,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue.shade200),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'This container has directional padding:',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 4),
                          Text('Start: 20px, End: 40px'),
                          Text('In LTR: Left=20px, Right=40px'),
                          Text('In RTL: Left=40px, Right=20px'),
                        ],
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.green.shade50,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.green.shade200),
                            ),
                            child: Column(
                              children: [
                                Text(
                                  'Text Align Start',
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                  textAlign: localizationService.textAlignStart,
                                ),
                                Text(
                                  'This text aligns to the start of the reading direction.',
                                  textAlign: localizationService.textAlignStart,
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.orange.shade50,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.orange.shade200),
                            ),
                            child: Column(
                              children: [
                                Text(
                                  'Text Align End',
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                  textAlign: localizationService.textAlignEnd,
                                ),
                                Text(
                                  'This text aligns to the end of the reading direction.',
                                  textAlign: localizationService.textAlignEnd,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // Action Buttons
                _buildInfoCard(
                  title: 'Actions',
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () => controller.toggleLanguage(),
                            icon: const Icon(Icons.swap_horiz),
                            label: Text('toggleLanguage'.tr),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () => controller.toggleThemeMode(),
                            icon: Icon(controller.isDarkMode ? Icons.light_mode : Icons.dark_mode),
                            label: Text(controller.isDarkMode ? 'lightMode'.tr : 'darkMode'.tr),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton.icon(
                        onPressed: () => _showDemoDialog(),
                        icon: const Icon(Icons.info),
                        label: Text('Show Demo Dialog'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          )),
      floatingActionButton: const LanguageFAB(mini: true),
    );
  }

  Widget _buildInfoCard({required String title, required List<Widget> children}) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTranslationExample(String key) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$key:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 12,
                fontFamily: 'monospace',
              ),
            ),
          ),
          Expanded(
            child: Text(
              key.tr,
              style: const TextStyle(color: Colors.blue),
            ),
          ),
        ],
      ),
    );
  }

  void _showDemoDialog() {
    Get.dialog(
      AlertDialog(
        title: Text('title'.tr),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('welcomeToDeewan'.tr),
            const SizedBox(height: 8),
            Text('appDescription'.tr),
            const SizedBox(height: 16),
            Text('This dialog demonstrates:'),
            Text('• Localized text'),
            Text('• RTL layout support'),
            Text('• Theme integration'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('cancel'.tr),
          ),
          ElevatedButton(
            onPressed: () => Get.back(),
            child: Text('ok'.tr),
          ),
        ],
      ),
    );
  }
}
