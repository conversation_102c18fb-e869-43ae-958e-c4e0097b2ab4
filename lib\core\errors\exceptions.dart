/// Represents errors thrown from the Data layer (DataSources).

/// Exception thrown when server returns an error response
class ServerException implements Exception {
  final String message;
  final int? statusCode;

  ServerException({required this.message, this.statusCode});

  @override
  String toString() => 'ServerException: $message (Status: $statusCode)';
}

/// Exception thrown when cache operations fail
class CacheException implements Exception {
  final String? message;

  CacheException({this.message});

  @override
  String toString() => 'CacheException: ${message ?? 'Cache operation failed'}';
}

/// Exception thrown when network operations fail
class NetworkException implements Exception {
  final String? message;

  NetworkException({this.message});

  @override
  String toString() =>
      'NetworkException: ${message ?? 'Network operation failed'}';
}

/// Exception thrown when validation fails
class ValidationException implements Exception {
  final String message;
  final String? field;

  ValidationException({required this.message, this.field});

  @override
  String toString() =>
      'ValidationException: $message${field != null ? ' (Field: $field)' : ''}';
}

/// Exception thrown when request times out
class TimeoutException implements Exception {
  final String? message;

  TimeoutException({this.message});

  @override
  String toString() => 'TimeoutException: ${message ?? 'Request timed out'}';
}

/// Exception thrown when user is not authorized
class UnauthorizedException implements Exception {
  final String? message;

  UnauthorizedException({this.message});

  @override
  String toString() =>
      'UnauthorizedException: ${message ?? 'Unauthorized access'}';
}

/// Exception thrown when user is forbidden from accessing resource
class ForbiddenException implements Exception {
  final String? message;

  ForbiddenException({this.message});

  @override
  String toString() => 'ForbiddenException: ${message ?? 'Access forbidden'}';
}

/// Exception thrown when data parsing fails
class DataParsingException implements Exception {
  final String? message;

  DataParsingException({this.message});

  @override
  String toString() =>
      'DataParsingException: ${message ?? 'Data parsing failed'}';
}

/// Exception thrown when file operations fail
class FileException implements Exception {
  final String? message;

  FileException({this.message});

  @override
  String toString() => 'FileException: ${message ?? 'File operation failed'}';
}

/// Exception thrown when database operations fail
class DatabaseException implements Exception {
  final String? message;

  DatabaseException({this.message});

  @override
  String toString() =>
      'DatabaseException: ${message ?? 'Database operation failed'}';
}

/// Exception thrown when authentication fails
class AuthenticationException implements Exception {
  final String? message;

  AuthenticationException({this.message});

  @override
  String toString() =>
      'AuthenticationException: ${message ?? 'Authentication failed'}';
}

/// Exception thrown when permission is denied
class PermissionException implements Exception {
  final String? message;

  PermissionException({this.message});

  @override
  String toString() => 'PermissionException: ${message ?? 'Permission denied'}';
}
